# 汉字大师 - <PERSON>zi Master Flashcard App

A comprehensive Chinese character learning app built with React Native, featuring 800 high-frequency Chinese characters and intelligent learning algorithms. Fully converted from web to native mobile experience for iPhone and iPad.

## 🎯 Core Features

### 📚 **Comprehensive Character Database**
- **800 High-Frequency Chinese Characters** from HSK levels 1-3 (expandable to level 5)
- **Scientifically Curated**: Based on cognitive science and spaced repetition principles
- **Rich Character Data**: Pinyin, meanings, stroke counts, components, and memory stories

### 🎮 **Three Interactive Learning Modes**
1. **Recognition Mode**: Character → Meaning/Pronunciation recall
2. **Writing Mode**: Practice character writing with stroke guidance
3. **Listening Mode**: Audio → Character identification

### 🧠 **Smart Learning System**
- **Adaptive Spaced Repetition**: Difficult characters appear more frequently
- **Progress Tracking**: Detailed statistics and learning analytics
- **Streak Counter**: Daily learning habit maintenance
- **Difficulty Adjustment**: AI-powered learning curve optimization

### 🎨 **Modern Mobile Experience**
- **Native React Native Performance**: Smooth 60fps animations
- **Dark/Light Mode**: Automatic theme switching with system preferences
- **Touch-Optimized Interface**: Designed for mobile-first interaction
- **Responsive Design**: Optimized for both iPhone and iPad screen sizes

### 🔊 **Audio & Accessibility**
- **Chinese Text-to-Speech**: Native pronunciation using expo-speech
- **Auto-play Options**: Configurable audio settings
- **Accessibility Support**: Screen reader compatible
- **Offline Capable**: Works without internet connection

## 📱 Platform Support

- ✅ **iPhone** (iOS 13.0+) - Optimized for all screen sizes
- ✅ **iPad** (iPadOS 13.0+) - Enhanced tablet experience
- ✅ **Android** (API 21+) - Full feature parity
- 🚀 **Cross-Platform**: Single codebase for all platforms

## 🚀 Quick Start Guide

### Prerequisites

- **Node.js** (v18 or later) - [Download here](https://nodejs.org/)
- **Expo CLI**: `npm install -g @expo/cli`
- **Mobile Device**: iPhone/iPad with Expo Go app from App Store

### Installation & Setup

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd Flashcard
   ```

2. **Install dependencies:**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Install required React Native packages:**
   ```bash
   npm install expo-speech react-native-svg
   ```

### 📱 Running on Device

#### Method 1: Expo Go (Recommended for Testing)

1. **Start development server:**
   ```bash
   npx expo start
   ```

2. **On your iPhone/iPad:**
   - Open **Expo Go** app
   - Scan the QR code from terminal/browser
   - App loads instantly on your device

#### Method 2: iOS Simulator (macOS only)

1. **Install Xcode** from Mac App Store
2. **Run simulator:**
   ```bash
   npx expo run:ios
   ```

#### Method 3: Android Testing

1. **Install Android Studio**
2. **Run on Android:**
   ```bash
   npx expo run:android
   ```

### 🏗️ Building for Production

#### iOS App Store

1. **Configure EAS Build:**
   ```bash
   npm install -g eas-cli
   eas login
   eas build:configure
   ```

2. **Build for iOS:**
   ```bash
   eas build --platform ios
   ```

#### Android Play Store

1. **Build for Android:**
   ```bash
   eas build --platform android
   ```

## 🛠️ Development & Architecture

### Project Structure

```
📁 Flashcard/
├── 📄 hanzi-flashcard-app.tsx    # Main React Native app component
├── 📄 App.tsx                    # Expo entry point (if using Expo)
├── 📄 package.json               # Dependencies and scripts
├── 📄 app.json                   # Expo configuration
├── 📄 README.md                  # This documentation
└── 📁 assets/                    # App icons and images
    ├── icon.png
    └── splash.png
```

### Core Technologies

#### **React Native Stack**
- **React Native**: Core mobile framework
- **TypeScript**: Type-safe development
- **Expo**: Development and build toolchain
- **React Hooks**: Modern state management

#### **Key Dependencies**
- **expo-speech**: Chinese text-to-speech synthesis
- **react-native-svg**: Custom icon components
- **React Native StyleSheet**: Optimized styling system

#### **Data Architecture**
- **Local State Management**: React hooks (useState, useEffect)
- **Persistent Storage**: Ready for AsyncStorage integration
- **Character Database**: 800 embedded Chinese characters
- **Progress Tracking**: Local JSON-based user progress

### App Architecture

```
🏗️ Component Hierarchy:
├── HanziFlashcardApp (Root)
├── ├── HomePage (Dashboard)
├── ├── LevelSelectPage (HSK Level Selection)
├── ├── StudyPage (Learning Interface)
├── ├── ResultsPage (Session Summary)
├── ├── ProgressPage (Statistics)
├── ├── FavoritesPage (Bookmarked Characters)
└── └── SettingsPage (Configuration)
```

## 📚 Learning System Deep Dive

### 🎯 HSK Level Progression
Currently implemented levels with expansion capability:

| Level | Characters | Difficulty | Status |
|-------|------------|------------|---------|
| **HSK 1** | 150 chars | Beginner | ✅ Active |
| **HSK 2** | 300 chars | Elementary | ✅ Active |
| **HSK 3** | 350 chars | Intermediate | ✅ Active |
| **HSK 4** | 600 chars | Upper-Int | 🔄 Ready |
| **HSK 5** | 1300 chars | Advanced | 🔄 Ready |

### 🎮 Interactive Learning Modes

#### 1. **Recognition Mode** 🔍
- **Display**: Chinese character
- **Task**: Recall pinyin and meaning
- **Skills**: Character recognition, reading comprehension
- **Feedback**: Instant pronunciation and meaning reveal

#### 2. **Writing Mode** ✍️
- **Display**: Pinyin and meaning
- **Task**: Write the correct character
- **Skills**: Character production, stroke order
- **Features**: Stroke guidance and practice area

#### 3. **Listening Mode** 👂
- **Display**: Audio pronunciation
- **Task**: Select correct character from options
- **Skills**: Listening comprehension, sound-character mapping
- **Technology**: Native Chinese TTS synthesis

### 🧠 Intelligent Learning Features

#### **Adaptive Spaced Repetition**
- Characters you struggle with appear more frequently
- Mastered characters have longer review intervals
- Algorithm adjusts based on your performance patterns

#### **Progress Analytics**
- **Session Statistics**: Accuracy, speed, completion rates
- **Long-term Tracking**: Learning curves, retention rates
- **Difficulty Analysis**: Identifies challenging character patterns
- **Streak Tracking**: Daily learning habit reinforcement

#### **Smart Recommendations**
- Suggests optimal study sessions based on performance
- Identifies characters needing review
- Recommends learning modes based on weak areas

## ⚙️ App Configuration & Settings

### 🎨 **Appearance Customization**
- **Theme Selection**:
  - 🌞 Light Mode: Clean, bright interface
  - 🌙 Dark Mode: Eye-friendly for low-light learning
  - 🔄 Auto: Follows system preferences
- **Typography**: Optimized Chinese character rendering
- **Color Schemes**: High contrast for accessibility

### 🎯 **Learning Preferences**
- **Daily Goals**: Customizable character targets (5-100 per day)
- **Session Length**: Adjustable study session duration
- **Difficulty Curve**: Adaptive or manual progression control
- **Review Frequency**: Spaced repetition interval customization

### 🔊 **Audio & Interaction Settings**
- **Audio Pronunciation**: Toggle Chinese TTS on/off
- **Auto-play**: Automatic pronunciation on card reveal
- **Hint System**: Show/hide learning aids and memory stories
- **Touch Feedback**: Haptic feedback for interactions

### 📱 **Mobile-Specific Features**
- **Gesture Controls**: Swipe navigation between cards
- **Orientation Support**: Portrait and landscape modes
- **Background Learning**: Continue progress when app is backgrounded
- **Notification Reminders**: Daily study reminders (configurable)

## � Data Management & Privacy

### 🔒 **Privacy-First Design**
- **100% Local Storage**: All data stays on your device
- **No Cloud Sync**: Complete privacy, no external servers
- **No Analytics**: No usage tracking or data collection
- **Offline Capable**: Full functionality without internet

### 📊 **Data Structure**
```typescript
UserProgress {
  characterId: {
    correct: number,      // Successful attempts
    incorrect: number,    // Failed attempts
    lastReviewed: Date,   // Last study session
    difficulty: number,   // Adaptive difficulty (1-5)
    streakCount: number   // Consecutive correct answers
  }
}
```

### 🔄 **Backup & Restore**
- **Export Progress**: JSON format for easy backup
- **Import Data**: Restore from previous backups
- **Reset Options**: Selective or complete progress reset
- **Migration Ready**: Future cloud sync preparation

## 🔧 Troubleshooting & Support

### 🚨 Common Issues & Solutions

#### **Development Issues**

1. **App won't load on device**
   ```bash
   # Solution 1: Check network connection
   # Ensure device and computer are on same WiFi

   # Solution 2: Restart Expo server
   npx expo start --clear

   # Solution 3: Reset Expo cache
   npx expo start --clear --reset-cache
   ```

2. **Dependencies not installing**
   ```bash
   # Clear npm cache and reinstall
   npm cache clean --force
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **TypeScript errors**
   ```bash
   # Install TypeScript dependencies
   npm install --save-dev typescript @types/react @types/react-native
   ```

#### **Runtime Issues**

1. **Audio not working**
   - ✅ Check device volume and mute switch
   - ✅ Grant microphone permissions in Settings
   - ✅ Test with other audio apps
   - ✅ Restart the app

2. **Performance issues**
   - ✅ Close background apps to free memory
   - ✅ Restart the app if sluggish
   - ✅ Check available device storage
   - ✅ Update to latest iOS/Android version

3. **Touch responsiveness**
   - ✅ Clean device screen
   - ✅ Remove screen protector if interfering
   - ✅ Check for iOS/Android touch sensitivity settings

### 📱 Platform-Specific Notes

#### **iOS Requirements**
- **Minimum**: iOS 13.0+ (iPhone 6s and newer)
- **Recommended**: iOS 15.0+ for best performance
- **iPad**: Optimized for all iPad models (2018+)
- **Storage**: 50MB free space required

#### **Android Requirements**
- **Minimum**: Android 7.0 (API 24)
- **Recommended**: Android 10+ for optimal experience
- **RAM**: 2GB minimum, 4GB recommended
- **Storage**: 50MB free space required

### 🆘 Getting Help

1. **Check Issues**: Search existing GitHub issues
2. **Create Issue**: Report bugs with device/OS details
3. **Community**: Join discussions for tips and tricks
4. **Documentation**: Refer to React Native and Expo docs

## � Future Roadmap

### 📋 Planned Features
- [ ] **Cloud Sync**: Cross-device progress synchronization
- [ ] **Social Learning**: Share progress with friends
- [ ] **Advanced Analytics**: ML-powered learning insights
- [ ] **Character Components**: Radical-based learning system
- [ ] **Handwriting Recognition**: AI-powered writing practice
- [ ] **Voice Recognition**: Pronunciation accuracy scoring
- [ ] **Gamification**: Achievements, leaderboards, challenges
- [ ] **Offline Dictionary**: Comprehensive character lookup

### 🔄 Version History
- **v1.0.0**: Initial React Native conversion with 800 characters
- **v0.9.0**: Web-based prototype with core functionality
- **v0.8.0**: Character database expansion to 800 characters

## 🤝 Contributing

We welcome contributions from the community! Here's how you can help:

### 🐛 **Bug Reports**
- Use GitHub Issues with detailed reproduction steps
- Include device model, OS version, and app version
- Attach screenshots or screen recordings if helpful

### 💡 **Feature Requests**
- Describe the feature and its educational value
- Explain how it would improve the learning experience
- Consider implementation complexity and user impact

### 🔧 **Code Contributions**
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request with detailed description

### 📚 **Content Contributions**
- Character data improvements (pinyin, meanings, stories)
- Translation accuracy enhancements
- Learning methodology suggestions

## � License

**MIT License** - This project is open source and free for educational use.

```
Copyright (c) 2024 Hanzi Master

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.
```

## 📞 Support & Community

### 🆘 **Getting Help**
- **GitHub Issues**: Technical problems and bug reports
- **Discussions**: Feature ideas and general questions
- **Documentation**: Comprehensive guides and tutorials

### 🌟 **Show Your Support**
- ⭐ Star this repository if you find it helpful
- 🐦 Share on social media to help others discover it
- 💝 Consider contributing to make it even better

---

## 🎉 **Start Your Chinese Learning Journey Today!**

**快乐学习！加油！** 🇨🇳📚✨

*"The best time to plant a tree was 20 years ago. The second best time is now."*
*- Chinese Proverb*

Ready to master Chinese characters? Download and start learning! 🚀
