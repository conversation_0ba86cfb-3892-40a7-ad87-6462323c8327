// Simple script to create placeholder assets
const fs = require('fs');
const path = require('path');

// Create a simple 1x1 transparent PNG as base64
const transparentPNG = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

// Create assets directory if it doesn't exist
if (!fs.existsSync('assets')) {
  fs.mkdirSync('assets');
}

// Create placeholder icon files
const iconFiles = [
  'icon.png',
  'adaptive-icon.png',
  'splash.png'
];

iconFiles.forEach(filename => {
  const filePath = path.join('assets', filename);
  if (!fs.existsSync(filePath)) {
    fs.writeFileSync(filePath, Buffer.from(transparentPNG, 'base64'));
    console.log(`Created ${filename}`);
  }
});

console.log('Asset files created successfully!');
