{"expo": {"name": "汉字大师 - Hanzi Master", "slug": "hanzi-flashcard-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#6366f1"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.hanzimaster.flashcard", "buildNumber": "1", "infoPlist": {"NSMicrophoneUsageDescription": "This app uses the microphone for speech recognition features.", "NSSpeechRecognitionUsageDescription": "This app uses speech recognition to help with pronunciation practice.", "CFBundleDisplayName": "汉字大师", "CFBundleName": "<PERSON><PERSON>"}, "config": {"usesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#6366f1"}, "package": "com.hanzimaster.flashcard", "versionCode": 1, "permissions": ["RECORD_AUDIO"]}, "web": {"bundler": "metro"}, "plugins": [], "extra": {"eas": {"projectId": "your-project-id-here"}}, "owner": "your-expo-username"}}