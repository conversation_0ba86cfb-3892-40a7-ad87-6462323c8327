import { useState, useEffect, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Dimensions,
  TextInput,
  Alert,
  StatusBar
} from 'react-native';
// AsyncStorage would be used for persistent storage in a real app
import * as Speech from 'expo-speech';
import Svg, { Path } from 'react-native-svg';



// 图标组件 - 使用SVG替代Lucide React
const IconBrain = ({ size = 24, color = '#6366f1' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M17.599 6.5a3 3 0 0 0 .399-1.375" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M6.003 5.125A3 3 0 0 0 6.401 6.5" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M3.477 10.896a4 4 0 0 1 .585-.396" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M19.938 10.5a4 4 0 0 1 .585.396" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M6 18a4 4 0 0 1-1.967-.516" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M19.967 17.484A4 4 0 0 1 18 18" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </Svg>
);

const IconStar = ({ size = 24, color = '#6366f1', filled = false }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill={filled ? color : "none"}>
    <Path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2Z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </Svg>
);

const IconVolume = ({ size = 24, color = '#6366f1' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M11 5 6 9H2v6h4l5 4V5Z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M15.54 8.46a5 5 0 0 1 0 7.07" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M19.07 4.93a10 10 0 0 1 0 14.14" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </Svg>
);

const IconEye = ({ size = 24, color = '#6366f1' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M12 9a3 3 0 1 1 0 6 3 3 0 0 1 0-6z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </Svg>
);

const IconPen = ({ size = 24, color = '#6366f1' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M12 19l7-7 3 3-7 7-3-3z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M2 2l7.586 7.586" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M11 11L2 2" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </Svg>
);

const IconChart = ({ size = 24, color = '#6366f1' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M3 3v18h18" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </Svg>
);

const IconSettings = ({ size = 24, color = '#6366f1' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.38a2 2 0 0 0-.73-2.73l-.15-.09a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.39a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </Svg>
);

const IconTrophy = ({ size = 24, color = '#6366f1' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M4 22h16" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M10 14.66V17c0 .55.45 1 1 1h2c.55 0 1-.45 1-1v-2.34" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M18 2H6v7a6 6 0 0 0 12 0V2Z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </Svg>
);

const IconClock = ({ size = 24, color = '#6366f1' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M12 6v6l4 2" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </Svg>
);

const IconTarget = ({ size = 24, color = '#6366f1' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M12 18a6 6 0 1 0 0-12 6 6 0 0 0 0 12z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M12 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </Svg>
);

const IconCalendar = ({ size = 24, color = '#6366f1' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M8 2v4" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M16 2v4" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M3 10h18" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M21 8.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8.5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </Svg>
);

const IconSearch = ({ size = 24, color = '#6366f1' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M11 19a8 8 0 1 0 0-16 8 8 0 0 0 0 16z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M21 21l-4.35-4.35" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </Svg>
);

const IconRotate = ({ size = 24, color = '#6366f1' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M21 3v5h-5" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M8 16l-5 5v-5h5" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </Svg>
);

const IconCheck = ({ size = 24, color = '#10b981' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M22 4L12 14.01l-3-3" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </Svg>
);

const IconX = ({ size = 24, color = '#ef4444' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M18 6L6 18" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M6 6l12 12" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </Svg>
);

const IconLightbulb = ({ size = 24, color = '#6366f1' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M9 21h6" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M12 17h0" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M12 3a6 6 0 0 0-6 6c0 1 .2 2 .6 2.8L9 15h6l2.4-3.2c.4-.8.6-1.8.6-2.8a6 6 0 0 0-6-6z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </Svg>
);

// 类型定义
interface HanziCard {
  id: number;
  character: string;
  pinyin: string;
  meaning: string;
  components: string[];
  story: string;
  level: number;
  strokes: number;
}

// Removed unused interfaces for cleaner code
type ScreenType = 'home' | 'study' | 'results' | 'progress' | 'settings' | 'levelSelect' | 'favorites';

// 汉字数据库 - 基于HSK等级和认知科学原理排序 (800个高频汉字) ✅ 已完成扩展
const hanziDatabase = [
  // HSK 1级 - 基础高频字 (1-150字)
  { id: 1, character: '的', pinyin: 'de', meaning: 'possessive particle', components: ['白', '勺'], story: 'White spoon shows possession', level: 1, strokes: 8 },
  { id: 2, character: '一', pinyin: 'yī', meaning: 'one', components: ['一'], story: 'Single horizontal line', level: 1, strokes: 1 },
  { id: 3, character: '是', pinyin: 'shì', meaning: 'to be', components: ['日', '正'], story: 'Sun above correct = is', level: 1, strokes: 9 },
  { id: 4, character: '不', pinyin: 'bù', meaning: 'not', components: ['不'], story: 'Bird cannot fly upward', level: 1, strokes: 4 },
  { id: 5, character: '了', pinyin: 'le', meaning: 'completed action', components: ['了'], story: 'Person completed a task', level: 1, strokes: 2 },
  { id: 6, character: '人', pinyin: 'rén', meaning: 'person, people', components: ['人'], story: 'Looks like a person walking', level: 1, strokes: 2 },
  { id: 7, character: '我', pinyin: 'wǒ', meaning: 'I, me', components: ['戈', '扌'], story: 'Hand holding weapon = I', level: 1, strokes: 7 },
  { id: 8, character: '在', pinyin: 'zài', meaning: 'at, in', components: ['土', '才'], story: 'Talent on earth = located at', level: 1, strokes: 6 },
  { id: 9, character: '有', pinyin: 'yǒu', meaning: 'to have', components: ['月', '又'], story: 'Moon and hand = to have', level: 1, strokes: 6 },
  { id: 10, character: '他', pinyin: 'tā', meaning: 'he, him', components: ['亻', '也'], story: 'Person + also = he too', level: 1, strokes: 5 },
  { id: 11, character: '这', pinyin: 'zhè', meaning: 'this', components: ['辶', '文'], story: 'Walking to text = this', level: 1, strokes: 7 },
  { id: 12, character: '中', pinyin: 'zhōng', meaning: 'middle, center', components: ['中'], story: 'Arrow through center of target', level: 1, strokes: 4 },
  { id: 13, character: '大', pinyin: 'dà', meaning: 'big, large', components: ['大'], story: 'A person spreading arms wide', level: 1, strokes: 3 },
  { id: 14, character: '来', pinyin: 'lái', meaning: 'to come', components: ['来'], story: 'Wheat coming from earth', level: 1, strokes: 7 },
  { id: 15, character: '上', pinyin: 'shàng', meaning: 'up, above', components: ['上'], story: 'Line above the base', level: 1, strokes: 3 },
  { id: 16, character: '国', pinyin: 'guó', meaning: 'country, nation', components: ['囗', '玉'], story: 'Jade treasure within borders', level: 1, strokes: 8 },
  { id: 17, character: '个', pinyin: 'gè', meaning: 'individual, classifier', components: ['人', '丶'], story: 'Person with a dot', level: 1, strokes: 3 },
  { id: 18, character: '到', pinyin: 'dào', meaning: 'to arrive', components: ['至', '刂'], story: 'Reach with a knife = arrive', level: 1, strokes: 8 },
  { id: 19, character: '说', pinyin: 'shuō', meaning: 'to speak', components: ['讠', '兑'], story: 'Words + exchange = speak', level: 1, strokes: 9 },
  { id: 20, character: '们', pinyin: 'men', meaning: 'plural marker', components: ['亻', '门'], story: 'Person at door = plural', level: 1, strokes: 5 },
  { id: 21, character: '为', pinyin: 'wéi', meaning: 'for, because of', components: ['为'], story: 'Elephant working for others', level: 1, strokes: 4 },
  { id: 22, character: '子', pinyin: 'zǐ', meaning: 'child, son', components: ['子'], story: 'Baby with arms outstretched', level: 1, strokes: 3 },
  { id: 23, character: '和', pinyin: 'hé', meaning: 'and, with', components: ['禾', '口'], story: 'Grain and mouth = harmony', level: 1, strokes: 8 },
  { id: 24, character: '你', pinyin: 'nǐ', meaning: 'you', components: ['亻', '尔'], story: 'Person that is you', level: 1, strokes: 7 },
  { id: 25, character: '地', pinyin: 'dì', meaning: 'earth, ground', components: ['土', '也'], story: 'Soil that is earth', level: 1, strokes: 6 },
  { id: 26, character: '出', pinyin: 'chū', meaning: 'to go out', components: ['凵', '山'], story: 'Mountain coming out of valley', level: 1, strokes: 5 },
  { id: 27, character: '道', pinyin: 'dào', meaning: 'road, way', components: ['辶', '首'], story: 'Walking on the first path', level: 1, strokes: 12 },
  { id: 28, character: '也', pinyin: 'yě', meaning: 'also, too', components: ['也'], story: 'Snake slithering also', level: 1, strokes: 3 },
  { id: 29, character: '时', pinyin: 'shí', meaning: 'time', components: ['日', '寺'], story: 'Sun at temple = time', level: 1, strokes: 7 },
  { id: 30, character: '年', pinyin: 'nián', meaning: 'year', components: ['年'], story: 'Person carrying grain = harvest year', level: 1, strokes: 6 },
  { id: 31, character: '得', pinyin: 'de', meaning: 'to get, obtain', components: ['彳', '旦', '寸'], story: 'Step at dawn with hand = obtain', level: 1, strokes: 11 },
  { id: 32, character: '就', pinyin: 'jiù', meaning: 'then, immediately', components: ['京', '尤'], story: 'Capital especially = then', level: 1, strokes: 12 },
  { id: 33, character: '那', pinyin: 'nà', meaning: 'that', components: ['冄', '阝'], story: 'Two people by hill = that', level: 1, strokes: 6 },
  { id: 34, character: '要', pinyin: 'yào', meaning: 'to want', components: ['西', '女'], story: 'Woman wanting west', level: 1, strokes: 9 },
  { id: 35, character: '下', pinyin: 'xià', meaning: 'down, below', components: ['下'], story: 'Line below the base', level: 1, strokes: 3 },
  { id: 36, character: '以', pinyin: 'yǐ', meaning: 'with, by means of', components: ['以'], story: 'Person using tool', level: 1, strokes: 4 },
  { id: 37, character: '生', pinyin: 'shēng', meaning: 'to be born, life', components: ['生'], story: 'Plant growing from earth', level: 1, strokes: 5 },
  { id: 38, character: '会', pinyin: 'huì', meaning: 'can, meeting', components: ['人', '云'], story: 'People under clouds meeting', level: 1, strokes: 6 },
  { id: 39, character: '家', pinyin: 'jiā', meaning: 'home, family', components: ['宀', '豕'], story: 'Pig under roof means home', level: 1, strokes: 10 },
  { id: 40, character: '可', pinyin: 'kě', meaning: 'can, may', components: ['丁', '口'], story: 'Nail in mouth = can do', level: 1, strokes: 5 },
  { id: 41, character: '她', pinyin: 'tā', meaning: 'she, her', components: ['女', '也'], story: 'Woman that is she', level: 1, strokes: 6 },
  { id: 42, character: '里', pinyin: 'lǐ', meaning: 'inside, village', components: ['田', '土'], story: 'Fields and earth = village', level: 1, strokes: 7 },
  { id: 43, character: '后', pinyin: 'hòu', meaning: 'after, behind', components: ['彳', '口'], story: 'Step and mouth = behind', level: 1, strokes: 6 },
  { id: 44, character: '小', pinyin: 'xiǎo', meaning: 'small, little', components: ['小'], story: 'Three small dots getting smaller', level: 1, strokes: 3 },
  { id: 45, character: '么', pinyin: 'me', meaning: 'what, question particle', components: ['幺'], story: 'Tiny thread = what?', level: 1, strokes: 3 },
  { id: 46, character: '心', pinyin: 'xīn', meaning: 'heart, mind', components: ['心'], story: 'Heart with chambers', level: 1, strokes: 4 },
  { id: 47, character: '多', pinyin: 'duō', meaning: 'many, much', components: ['夕', '夕'], story: 'Two evenings = many', level: 1, strokes: 6 },
  { id: 48, character: '天', pinyin: 'tiān', meaning: 'day, sky', components: ['一', '大'], story: 'Big line above = sky', level: 1, strokes: 4 },
  { id: 49, character: '而', pinyin: 'ér', meaning: 'and, but', components: ['而'], story: 'Whiskers and = but', level: 1, strokes: 6 },
  { id: 50, character: '能', pinyin: 'néng', meaning: 'can, able', components: ['月', '匕', '匕'], story: 'Moon and two spoons = able', level: 1, strokes: 10 },

  // 继续HSK 1级
  { id: 51, character: '好', pinyin: 'hǎo', meaning: 'good, well', components: ['女', '子'], story: 'Woman and child = good', level: 1, strokes: 6 },
  { id: 52, character: '都', pinyin: 'dōu', meaning: 'all, both', components: ['者', '阝'], story: 'Person by city = all', level: 1, strokes: 10 },
  { id: 53, character: '然', pinyin: 'rán', meaning: 'so, like that', components: ['月', '犬', '灬'], story: 'Moon, dog, fire = natural', level: 1, strokes: 12 },
  { id: 54, character: '没', pinyin: 'méi', meaning: 'not have', components: ['氵', '殳'], story: 'Water without weapon = nothing', level: 1, strokes: 7 },
  { id: 55, character: '日', pinyin: 'rì', meaning: 'day, sun', components: ['日'], story: 'Square sun with center dot', level: 1, strokes: 4 },
  { id: 56, character: '对', pinyin: 'duì', meaning: 'correct, pair', components: ['又', '寸'], story: 'Hand and inch = correct', level: 1, strokes: 5 },
  { id: 57, character: '起', pinyin: 'qǐ', meaning: 'to rise, start', components: ['走', '己'], story: 'Walking oneself = to start', level: 1, strokes: 10 },
  { id: 58, character: '还', pinyin: 'hái', meaning: 'still, yet', components: ['辶', '不'], story: 'Walking not = still going', level: 1, strokes: 7 },
  { id: 59, character: '发', pinyin: 'fā', meaning: 'to send, emit', components: ['癶', '友'], story: 'Footsteps of friend = send', level: 1, strokes: 5 },
  { id: 60, character: '成', pinyin: 'chéng', meaning: 'to become', components: ['戊', '丁'], story: 'Weapon and nail = accomplish', level: 1, strokes: 6 },
  { id: 61, character: '事', pinyin: 'shì', meaning: 'matter, thing', components: ['一', '口', '亅'], story: 'Line, mouth, hook = matter', level: 1, strokes: 8 },
  { id: 62, character: '只', pinyin: 'zhǐ', meaning: 'only, just', components: ['口', '八'], story: 'Mouth and eight = only', level: 1, strokes: 5 },
  { id: 63, character: '作', pinyin: 'zuò', meaning: 'to do, make', components: ['亻', '乍'], story: 'Person working suddenly = to do', level: 1, strokes: 7 },
  { id: 64, character: '当', pinyin: 'dāng', meaning: 'when, should', components: ['彐', '田'], story: 'Snout over field = when', level: 1, strokes: 6 },
  { id: 65, character: '想', pinyin: 'xiǎng', meaning: 'to think', components: ['相', '心'], story: 'Face and heart = to think', level: 1, strokes: 13 },
  { id: 66, character: '看', pinyin: 'kàn', meaning: 'to see, look', components: ['手', '目'], story: 'Hand over eye = to look', level: 1, strokes: 9 },
  { id: 67, character: '文', pinyin: 'wén', meaning: 'text, culture', components: ['文'], story: 'Crossed lines = writing', level: 1, strokes: 4 },
  { id: 68, character: '无', pinyin: 'wú', meaning: 'without, none', components: ['无'], story: 'Person dancing = nothing', level: 1, strokes: 4 },
  { id: 69, character: '开', pinyin: 'kāi', meaning: 'to open', components: ['廾', '一'], story: 'Two hands lifting bar = open', level: 1, strokes: 4 },
  { id: 70, character: '手', pinyin: 'shǒu', meaning: 'hand', components: ['手'], story: 'Palm and fingers', level: 1, strokes: 4 },
  { id: 71, character: '十', pinyin: 'shí', meaning: 'ten', components: ['十'], story: 'Cross shape = ten', level: 1, strokes: 2 },
  { id: 72, character: '用', pinyin: 'yòng', meaning: 'to use', components: ['用'], story: 'Bucket for use', level: 1, strokes: 5 },
  { id: 73, character: '主', pinyin: 'zhǔ', meaning: 'main, master', components: ['丶', '王'], story: 'Dot over king = master', level: 1, strokes: 5 },
  { id: 74, character: '行', pinyin: 'xíng', meaning: 'to walk, ok', components: ['彳', '亍'], story: 'Left and right step = walk', level: 1, strokes: 6 },
  { id: 75, character: '方', pinyin: 'fāng', meaning: 'square, direction', components: ['方'], story: 'Square with dot = direction', level: 1, strokes: 4 },
  { id: 76, character: '又', pinyin: 'yòu', meaning: 'again, also', components: ['又'], story: 'Right hand = again', level: 1, strokes: 2 },
  { id: 77, character: '如', pinyin: 'rú', meaning: 'like, as', components: ['女', '口'], story: 'Woman\'s mouth = like', level: 1, strokes: 6 },
  { id: 78, character: '前', pinyin: 'qián', meaning: 'front, before', components: ['刖', '刂'], story: 'Foot cut off = front', level: 1, strokes: 9 },
  { id: 79, character: '所', pinyin: 'suǒ', meaning: 'place, that which', components: ['户', '斤'], story: 'Door and axe = place', level: 1, strokes: 8 },
  { id: 80, character: '本', pinyin: 'běn', meaning: 'book, origin', components: ['木', '一'], story: 'Tree with line = root/book', level: 1, strokes: 5 },
  { id: 81, character: '见', pinyin: 'jiàn', meaning: 'to see', components: ['目', '儿'], story: 'Eye with legs = to see', level: 1, strokes: 4 },
  { id: 82, character: '经', pinyin: 'jīng', meaning: 'through, classic', components: ['纟', '圣'], story: 'Thread and sage = classic', level: 1, strokes: 8 },
  { id: 83, character: '头', pinyin: 'tóu', meaning: 'head', components: ['大', '丶'], story: 'Big with dot = head', level: 1, strokes: 5 },
  { id: 84, character: '面', pinyin: 'miàn', meaning: 'face, surface', components: ['面'], story: 'Square face outline', level: 1, strokes: 9 },
  { id: 85, character: '公', pinyin: 'gōng', meaning: 'public, male', components: ['八', '厶'], story: 'Eight and private = public', level: 1, strokes: 4 },
  { id: 86, character: '同', pinyin: 'tóng', meaning: 'same, together', components: ['冂', '一', '口'], story: 'Frame with line and mouth = same', level: 1, strokes: 6 },
  { id: 87, character: '三', pinyin: 'sāam', meaning: 'three', components: ['三'], story: 'Three horizontal lines', level: 1, strokes: 3 },
  { id: 88, character: '已', pinyin: 'yǐ', meaning: 'already', components: ['已'], story: 'Snake coiled = already done', level: 1, strokes: 3 },
  { id: 89, character: '老', pinyin: 'lǎo', meaning: 'old', components: ['老'], story: 'Person with bent back = old', level: 1, strokes: 6 },
  { id: 90, character: '从', pinyin: 'cóng', meaning: 'from, follow', components: ['人', '人'], story: 'Person following person', level: 1, strokes: 4 },
  { id: 91, character: '动', pinyin: 'dòng', meaning: 'to move', components: ['云', '力'], story: 'Cloud with force = movement', level: 1, strokes: 6 },
  { id: 92, character: '两', pinyin: 'liǎng', meaning: 'two, both', components: ['一', '冂', '山'], story: 'One frame two mountains = two', level: 1, strokes: 7 },
  { id: 93, character: '长', pinyin: 'cháng', meaning: 'long', components: ['长'], story: 'Long hair flowing = long', level: 1, strokes: 4 },
  { id: 94, character: '回', pinyin: 'huí', meaning: 'to return', components: ['囗', '口'], story: 'Mouth within frame = return', level: 1, strokes: 6 },
  { id: 95, character: '什', pinyin: 'shén', meaning: 'what', components: ['亻', '十'], story: 'Person and ten = what', level: 1, strokes: 4 },
  { id: 96, character: '二', pinyin: 'èr', meaning: 'two', components: ['二'], story: 'Two horizontal lines', level: 1, strokes: 2 },
  { id: 97, character: '水', pinyin: 'shuǐ', meaning: 'water', components: ['水'], story: 'Flowing water with drops', level: 1, strokes: 4 },
  { id: 98, character: '新', pinyin: 'xīn', meaning: 'new', components: ['亲', '斤'], story: 'Close with axe = new cut', level: 1, strokes: 13 },
  { id: 99, character: '手', pinyin: 'shǒu', meaning: 'hand', components: ['手'], story: 'Palm with fingers', level: 1, strokes: 4 },
  { id: 100, character: '高', pinyin: 'gāo', meaning: 'tall, high', components: ['亠', '口', '冂', '口'], story: 'Tower with doors = high', level: 1, strokes: 10 },

  // HSK 2级开始 (151-300字)
  { id: 101, character: '学', pinyin: 'xué', meaning: 'study, learn', components: ['学'], story: 'Child under roof learning', level: 2, strokes: 8 },
  { id: 102, character: '自', pinyin: 'zì', meaning: 'self, from', components: ['自'], story: 'Nose pointing to self', level: 2, strokes: 6 },
  { id: 103, character: '分', pinyin: 'fēn', meaning: 'divide, minute', components: ['八', '刀'], story: 'Eight divided by knife', level: 2, strokes: 4 },
  { id: 104, character: '总', pinyin: 'zǒng', meaning: 'total, always', components: ['悤', '心'], story: 'Hurried heart = always total', level: 2, strokes: 9 },
  { id: 105, character: '给', pinyin: 'gěi', meaning: 'to give', components: ['纟', '合'], story: 'Thread coming together = give', level: 2, strokes: 9 },
  { id: 106, character: '身', pinyin: 'shēn', meaning: 'body', components: ['身'], story: 'Pregnant person = body', level: 2, strokes: 7 },
  { id: 107, character: '此', pinyin: 'cǐ', meaning: 'this, here', components: ['止', '匕'], story: 'Stop and spoon = this', level: 2, strokes: 6 },
  { id: 108, character: '其', pinyin: 'qí', meaning: 'his, her, its', components: ['其'], story: 'Basket woven = its', level: 2, strokes: 8 },
  { id: 109, character: '安', pinyin: 'ān', meaning: 'safe, peaceful', components: ['宀', '女'], story: 'Woman under roof = safe', level: 2, strokes: 6 },
  { id: 110, character: '今', pinyin: 'jīn', meaning: 'now, today', components: ['人', '一'], story: 'Person over line = now', level: 2, strokes: 4 },
  { id: 111, character: '次', pinyin: 'cì', meaning: 'time, order', components: ['冫', '欠'], story: 'Ice and yawn = next time', level: 2, strokes: 6 },
  { id: 112, character: '使', pinyin: 'shǐ', meaning: 'to use, make', components: ['亻', '吏'], story: 'Person as official = to use', level: 2, strokes: 8 },
  { id: 113, character: '间', pinyin: 'jiān', meaning: 'between, room', components: ['门', '日'], story: 'Sun through door = between', level: 2, strokes: 7 },
  { id: 114, character: '理', pinyin: 'lǐ', meaning: 'reason, logic', components: ['王', '里'], story: 'King in village = reason', level: 2, strokes: 11 },
  { id: 115, character: '明', pinyin: 'míng', meaning: 'bright, clear', components: ['日', '月'], story: 'Sun and moon = bright', level: 2, strokes: 8 },
  { id: 116, character: '性', pinyin: 'xìng', meaning: 'nature, character', components: ['忄', '生'], story: 'Heart and birth = nature', level: 2, strokes: 8 },
  { id: 117, character: '知', pinyin: 'zhī', meaning: 'to know', components: ['矢', '口'], story: 'Arrow to mouth = to know', level: 2, strokes: 8 },
  { id: 118, character: '国', pinyin: 'guó', meaning: 'country', components: ['囗', '玉'], story: 'Jade within borders', level: 2, strokes: 8 },
  { id: 119, character: '意', pinyin: 'yì', meaning: 'meaning, intention', components: ['立', '日', '心'], story: 'Stand, sun, heart = meaning', level: 2, strokes: 13 },

  // 继续HSK 2级
  { id: 120, character: '问', pinyin: 'wèn', meaning: 'to ask', components: ['门', '口'], story: 'Mouth at door = to ask', level: 2, strokes: 6 },
  { id: 121, character: '很', pinyin: 'hěn', meaning: 'very', components: ['彳', '艮'], story: 'Step with determination = very', level: 2, strokes: 9 },
  { id: 122, character: '进', pinyin: 'jìn', meaning: 'to enter', components: ['辶', '井'], story: 'Walking to well = enter', level: 2, strokes: 7 },
  { id: 123, character: '种', pinyin: 'zhǒng', meaning: 'kind, type', components: ['禾', '中'], story: 'Grain in center = type', level: 2, strokes: 9 },
  { id: 124, character: '将', pinyin: 'jiāng', meaning: 'will, shall', components: ['丬', '夕', '寸'], story: 'Bed, evening, hand = will', level: 2, strokes: 9 },
  { id: 125, character: '各', pinyin: 'gè', meaning: 'each, every', components: ['夂', '口'], story: 'Walking to mouth = each', level: 2, strokes: 6 },
  { id: 126, character: '重', pinyin: 'zhòng', meaning: 'heavy, important', components: ['千', '里'], story: 'Thousand miles = heavy', level: 2, strokes: 9 },
  { id: 127, character: '线', pinyin: 'xiàn', meaning: 'line, thread', components: ['纟', '戋'], story: 'Thread cut small = line', level: 2, strokes: 8 },
  { id: 128, character: '内', pinyin: 'nèi', meaning: 'inside, within', components: ['冂', '人'], story: 'Person in frame = inside', level: 2, strokes: 4 },
  { id: 129, character: '数', pinyin: 'shù', meaning: 'number, count', components: ['米', '攵'], story: 'Rice being counted = number', level: 2, strokes: 13 },
  { id: 130, character: '正', pinyin: 'zhèng', meaning: 'correct, right', components: ['一', '止'], story: 'One stop = correct', level: 2, strokes: 5 },
  { id: 131, character: '反', pinyin: 'fǎn', meaning: 'opposite, reverse', components: ['厂', '又'], story: 'Cliff and hand = reverse', level: 2, strokes: 4 },
  { id: 132, character: '任', pinyin: 'rèn', meaning: 'to allow, duty', components: ['亻', '壬'], story: 'Person with burden = duty', level: 2, strokes: 6 },
  { id: 133, character: '件', pinyin: 'jiàn', meaning: 'item, piece', components: ['亻', '牛'], story: 'Person and cow = item', level: 2, strokes: 6 },
  { id: 134, character: '因', pinyin: 'yīn', meaning: 'because, cause', components: ['囗', '大'], story: 'Big in frame = because', level: 2, strokes: 6 },
  { id: 135, character: '定', pinyin: 'dìng', meaning: 'fixed, certain', components: ['宀', '正'], story: 'Correct under roof = fixed', level: 2, strokes: 8 },
  { id: 136, character: '机', pinyin: 'jī', meaning: 'machine, opportunity', components: ['木', '几'], story: 'Wood table = machine', level: 2, strokes: 6 },
  { id: 137, character: '工', pinyin: 'gōng', meaning: 'work, labor', components: ['工'], story: 'Tool for work', level: 2, strokes: 3 },
  { id: 138, character: '调', pinyin: 'diào', meaning: 'to adjust, tune', components: ['讠', '周'], story: 'Words around = adjust', level: 2, strokes: 10 },
  { id: 139, character: '并', pinyin: 'bìng', meaning: 'and, together', components: ['并'], story: 'Two people together', level: 2, strokes: 6 },
  { id: 140, character: '外', pinyin: 'wài', meaning: 'outside, foreign', components: ['夕', '卜'], story: 'Evening divination = outside', level: 2, strokes: 5 },
  { id: 141, character: '者', pinyin: 'zhě', meaning: 'person, one who', components: ['老', '日'], story: 'Old sun = wise person', level: 2, strokes: 8 },
  { id: 142, character: '相', pinyin: 'xiāng', meaning: 'mutual, each other', components: ['木', '目'], story: 'Tree and eye = observe each other', level: 2, strokes: 9 },
  { id: 143, character: '管', pinyin: 'guǎn', meaning: 'to manage, tube', components: ['竹', '官'], story: 'Bamboo official = manage', level: 2, strokes: 14 },
  { id: 144, character: '思', pinyin: 'sī', meaning: 'to think', components: ['田', '心'], story: 'Field and heart = think', level: 2, strokes: 9 },
  { id: 145, character: '位', pinyin: 'wèi', meaning: 'position, place', components: ['亻', '立'], story: 'Person standing = position', level: 2, strokes: 7 },
  { id: 146, character: '教', pinyin: 'jiāo', meaning: 'to teach', components: ['孝', '攵'], story: 'Filial piety taught = teach', level: 2, strokes: 11 },
  { id: 147, character: '广', pinyin: 'guǎng', meaning: 'wide, broad', components: ['广'], story: 'Shelter extending = wide', level: 2, strokes: 3 },
  { id: 148, character: '首', pinyin: 'shǒu', meaning: 'head, first', components: ['首'], story: 'Head with hair = first', level: 2, strokes: 9 },
  { id: 149, character: '电', pinyin: 'diàn', meaning: 'electricity', components: ['雨', '电'], story: 'Rain and lightning = electricity', level: 2, strokes: 5 },
  { id: 150, character: '常', pinyin: 'cháng', meaning: 'often, usual', components: ['尚', '巾'], story: 'Still cloth = often', level: 2, strokes: 11 },

  // HSK 3级开始 (301-500字)
  { id: 151, character: '路', pinyin: 'lù', meaning: 'road, path', components: ['足', '各'], story: 'Foot going each way = road', level: 3, strokes: 13 },
  { id: 152, character: '科', pinyin: 'kē', meaning: 'science, subject', components: ['禾', '斗'], story: 'Grain and measure = science', level: 3, strokes: 9 },
  { id: 153, character: '去', pinyin: 'qù', meaning: 'to go', components: ['土', '厶'], story: 'Earth and private = go', level: 3, strokes: 5 },
  { id: 154, character: '力', pinyin: 'lì', meaning: 'strength, power', components: ['力'], story: 'Muscle flexing = strength', level: 3, strokes: 2 },
  { id: 155, character: '神', pinyin: 'shén', meaning: 'god, spirit', components: ['示', '申'], story: 'Show and extend = god', level: 3, strokes: 9 },
  { id: 156, character: '半', pinyin: 'bàn', meaning: 'half', components: ['八', '牛'], story: 'Eight cow = half', level: 3, strokes: 5 },
  { id: 157, character: '火', pinyin: 'huǒ', meaning: 'fire', components: ['火'], story: 'Flames rising up', level: 3, strokes: 4 },
  { id: 158, character: '南', pinyin: 'nán', meaning: 'south', components: ['十', '冂', '干'], story: 'Ten in frame dry = south', level: 3, strokes: 9 },
  { id: 159, character: '写', pinyin: 'xiě', meaning: 'to write', components: ['冖', '与'], story: 'Cover and give = write', level: 3, strokes: 5 },
  { id: 160, character: '部', pinyin: 'bù', meaning: 'part, section', components: ['立', '口', '阝'], story: 'Stand mouth city = part', level: 3, strokes: 10 },
  { id: 161, character: '等', pinyin: 'děng', meaning: 'to wait, class', components: ['竹', '寺'], story: 'Bamboo temple = wait', level: 3, strokes: 12 },
  { id: 162, character: '住', pinyin: 'zhù', meaning: 'to live, stay', components: ['亻', '主'], story: 'Person master = live', level: 3, strokes: 7 },
  { id: 163, character: '百', pinyin: 'bǎi', meaning: 'hundred', components: ['一', '白'], story: 'One white = hundred', level: 3, strokes: 6 },
  { id: 164, character: '改', pinyin: 'gǎi', meaning: 'to change', components: ['己', '攵'], story: 'Self being hit = change', level: 3, strokes: 7 },
  { id: 165, character: '先', pinyin: 'xiān', meaning: 'first, before', components: ['儿', '土'], story: 'Child on earth = first', level: 3, strokes: 6 },
  { id: 166, character: '风', pinyin: 'fēng', meaning: 'wind', components: ['几', '虫'], story: 'Table insect = wind', level: 3, strokes: 4 },
  { id: 167, character: '议', pinyin: 'yì', meaning: 'to discuss', components: ['讠', '义'], story: 'Words of righteousness = discuss', level: 3, strokes: 5 },
  { id: 168, character: '往', pinyin: 'wǎng', meaning: 'to go towards', components: ['彳', '王'], story: 'Step to king = go towards', level: 3, strokes: 8 },
  { id: 169, character: '元', pinyin: 'yuán', meaning: 'first, dollar', components: ['一', '兀'], story: 'One sitting = first', level: 3, strokes: 4 },
  { id: 170, character: '办', pinyin: 'bàn', meaning: 'to handle', components: ['力', '办'], story: 'Strength to handle', level: 3, strokes: 4 },
  { id: 171, character: '民', pinyin: 'mín', meaning: 'people, citizen', components: ['民'], story: 'Eye with line = people', level: 3, strokes: 5 },
  { id: 172, character: '结', pinyin: 'jié', meaning: 'to tie, result', components: ['纟', '吉'], story: 'Thread lucky = tie', level: 3, strokes: 9 },
  { id: 173, character: '解', pinyin: 'jiě', meaning: 'to solve', components: ['角', '刀', '牛'], story: 'Horn knife cow = solve', level: 3, strokes: 13 },
  { id: 174, character: '步', pinyin: 'bù', meaning: 'step, pace', components: ['止', '少'], story: 'Stop few = step', level: 3, strokes: 7 },
  { id: 175, character: '确', pinyin: 'què', meaning: 'certain, sure', components: ['石', '隹'], story: 'Stone bird = certain', level: 3, strokes: 12 },
  { id: 176, character: '级', pinyin: 'jí', meaning: 'level, grade', components: ['纟', '及'], story: 'Thread reach = level', level: 3, strokes: 6 },
  { id: 177, character: '参', pinyin: 'cān', meaning: 'to participate', components: ['厶', '大', '彡'], story: 'Private big hair = participate', level: 3, strokes: 8 },
  { id: 178, character: '绝', pinyin: 'jué', meaning: 'absolute, cut off', components: ['纟', '色'], story: 'Thread color = absolute', level: 3, strokes: 9 },
  { id: 179, character: '愿', pinyin: 'yuàn', meaning: 'to wish', components: ['原', '心'], story: 'Original heart = wish', level: 3, strokes: 14 },
  { id: 180, character: '技', pinyin: 'jì', meaning: 'skill, technique', components: ['扌', '支'], story: 'Hand support = skill', level: 3, strokes: 7 },
  { id: 181, character: '建', pinyin: 'jiàn', meaning: 'to build', components: ['廴', '聿'], story: 'Walk and brush = build', level: 3, strokes: 8 },
  { id: 182, character: '空', pinyin: 'kōng', meaning: 'empty, sky', components: ['穴', '工'], story: 'Cave work = empty', level: 3, strokes: 8 },
  { id: 183, character: '合', pinyin: 'hé', meaning: 'to combine', components: ['人', '一', '口'], story: 'Person one mouth = combine', level: 3, strokes: 6 },
  { id: 184, character: '目', pinyin: 'mù', meaning: 'eye', components: ['目'], story: 'Eye looking sideways', level: 3, strokes: 5 },
  { id: 185, character: '领', pinyin: 'lǐng', meaning: 'to lead', components: ['令', '页'], story: 'Order page = lead', level: 3, strokes: 11 },
  { id: 186, character: '呢', pinyin: 'ne', meaning: 'question particle', components: ['口', '尼'], story: 'Mouth nun = question', level: 3, strokes: 8 },
  { id: 187, character: '味', pinyin: 'wèi', meaning: 'taste, flavor', components: ['口', '未'], story: 'Mouth not yet = taste', level: 3, strokes: 8 },
  { id: 188, character: '指', pinyin: 'zhǐ', meaning: 'finger, to point', components: ['扌', '旨'], story: 'Hand purpose = point', level: 3, strokes: 9 },
  { id: 189, character: '月', pinyin: 'yuè', meaning: 'moon, month', components: ['月'], story: 'Crescent moon', level: 3, strokes: 4 },
  { id: 190, character: '言', pinyin: 'yán', meaning: 'speech, words', components: ['言'], story: 'Mouth with lines = speech', level: 3, strokes: 7 },
  { id: 191, character: '花', pinyin: 'huā', meaning: 'flower', components: ['艹', '化'], story: 'Grass changing = flower', level: 3, strokes: 7 },
  { id: 192, character: '受', pinyin: 'shòu', meaning: 'to receive', components: ['爫', '冖', '又'], story: 'Claw cover hand = receive', level: 3, strokes: 8 },
  { id: 193, character: '作', pinyin: 'zuò', meaning: 'to do, work', components: ['亻', '乍'], story: 'Person working suddenly', level: 3, strokes: 7 },
  { id: 194, character: '建', pinyin: 'jiàn', meaning: 'to build', components: ['廴', '聿'], story: 'Walk and brush = build', level: 3, strokes: 8 },
  { id: 195, character: '活', pinyin: 'huó', meaning: 'to live, alive', components: ['氵', '舌'], story: 'Water tongue = alive', level: 3, strokes: 9 },
  { id: 196, character: '话', pinyin: 'huà', meaning: 'speech, words', components: ['讠', '舌'], story: 'Words tongue = speech', level: 3, strokes: 8 },
  { id: 197, character: '回', pinyin: 'huí', meaning: 'to return', components: ['囗', '口'], story: 'Mouth in frame = return', level: 3, strokes: 6 },
  { id: 198, character: '事', pinyin: 'shì', meaning: 'matter, affair', components: ['一', '口', '亅'], story: 'Line mouth hook = matter', level: 3, strokes: 8 },
  { id: 199, character: '起', pinyin: 'qǐ', meaning: 'to rise, start', components: ['走', '己'], story: 'Walking oneself = start', level: 3, strokes: 10 },
  { id: 200, character: '最', pinyin: 'zuì', meaning: 'most', components: ['日', '取'], story: 'Sun take = most', level: 3, strokes: 12 },

  // 继续HSK 3级 (201-300)
  { id: 201, character: '北', pinyin: 'běi', meaning: 'north', components: ['匕', '匕'], story: 'Two spoons pointing north', level: 3, strokes: 5 },
  { id: 202, character: '东', pinyin: 'dōng', meaning: 'east', components: ['木'], story: 'Tree where sun rises = east', level: 3, strokes: 5 },
  { id: 203, character: '西', pinyin: 'xī', meaning: 'west', components: ['西'], story: 'Basket where sun sets = west', level: 3, strokes: 6 },
  { id: 204, character: '南', pinyin: 'nán', meaning: 'south', components: ['十', '冂', '干'], story: 'Ten in frame dry = south', level: 3, strokes: 9 },
  { id: 205, character: '左', pinyin: 'zuǒ', meaning: 'left', components: ['工', '又'], story: 'Work with hand = left', level: 3, strokes: 5 },
  { id: 206, character: '右', pinyin: 'yòu', meaning: 'right', components: ['口', '又'], story: 'Mouth and hand = right', level: 3, strokes: 5 },
  { id: 207, character: '远', pinyin: 'yuǎn', meaning: 'far', components: ['辶', '元'], story: 'Walking to origin = far', level: 3, strokes: 7 },
  { id: 208, character: '近', pinyin: 'jìn', meaning: 'near', components: ['辶', '斤'], story: 'Walking with axe = near', level: 3, strokes: 7 },
  { id: 209, character: '快', pinyin: 'kuài', meaning: 'fast', components: ['忄', '夬'], story: 'Heart deciding = fast', level: 3, strokes: 7 },
  { id: 210, character: '慢', pinyin: 'màn', meaning: 'slow', components: ['忄', '曼'], story: 'Heart graceful = slow', level: 3, strokes: 14 },
  { id: 211, character: '早', pinyin: 'zǎo', meaning: 'early', components: ['日', '十'], story: 'Sun and ten = early', level: 3, strokes: 6 },
  { id: 212, character: '晚', pinyin: 'wǎn', meaning: 'late', components: ['日', '免'], story: 'Sun avoiding = late', level: 3, strokes: 11 },
  { id: 213, character: '上午', pinyin: 'shàngwǔ', meaning: 'morning', components: ['上', '午'], story: 'Above noon = morning', level: 3, strokes: 7 },
  { id: 214, character: '下午', pinyin: 'xiàwǔ', meaning: 'afternoon', components: ['下', '午'], story: 'Below noon = afternoon', level: 3, strokes: 7 },
  { id: 215, character: '今天', pinyin: 'jīntiān', meaning: 'today', components: ['今', '天'], story: 'Now day = today', level: 3, strokes: 8 },
  { id: 216, character: '明天', pinyin: 'míngtiān', meaning: 'tomorrow', components: ['明', '天'], story: 'Bright day = tomorrow', level: 3, strokes: 12 },
  { id: 217, character: '昨天', pinyin: 'zuótiān', meaning: 'yesterday', components: ['昨', '天'], story: 'Past day = yesterday', level: 3, strokes: 13 },
  { id: 218, character: '春', pinyin: 'chūn', meaning: 'spring', components: ['日', '屯'], story: 'Sun sprouting = spring', level: 3, strokes: 9 },
  { id: 219, character: '夏', pinyin: 'xià', meaning: 'summer', components: ['夂', '自'], story: 'Walking self = summer', level: 3, strokes: 10 },
  { id: 220, character: '秋', pinyin: 'qiū', meaning: 'autumn', components: ['禾', '火'], story: 'Grain and fire = autumn', level: 3, strokes: 9 },
  { id: 221, character: '冬', pinyin: 'dōng', meaning: 'winter', components: ['夂', '冫'], story: 'Walking ice = winter', level: 3, strokes: 5 },
  { id: 222, character: '热', pinyin: 'rè', meaning: 'hot', components: ['执', '灬'], story: 'Hold fire = hot', level: 3, strokes: 10 },
  { id: 223, character: '冷', pinyin: 'lěng', meaning: 'cold', components: ['冫', '令'], story: 'Ice order = cold', level: 3, strokes: 7 },
  { id: 224, character: '暖', pinyin: 'nuǎn', meaning: 'warm', components: ['日', '爰'], story: 'Sun helping = warm', level: 3, strokes: 13 },
  { id: 225, character: '凉', pinyin: 'liáng', meaning: 'cool', components: ['冫', '京'], story: 'Ice capital = cool', level: 3, strokes: 10 },
  { id: 226, character: '晴', pinyin: 'qíng', meaning: 'sunny', components: ['日', '青'], story: 'Sun green = sunny', level: 3, strokes: 12 },
  { id: 227, character: '阴', pinyin: 'yīn', meaning: 'cloudy', components: ['阝', '今'], story: 'Hill now = cloudy', level: 3, strokes: 6 },
  { id: 228, character: '雨', pinyin: 'yǔ', meaning: 'rain', components: ['雨'], story: 'Drops falling from clouds', level: 3, strokes: 8 },
  { id: 229, character: '雪', pinyin: 'xuě', meaning: 'snow', components: ['雨', '彗'], story: 'Rain broom = snow', level: 3, strokes: 11 },
  { id: 230, character: '风', pinyin: 'fēng', meaning: 'wind', components: ['几', '虫'], story: 'Table insect = wind', level: 3, strokes: 4 },
  { id: 231, character: '云', pinyin: 'yún', meaning: 'cloud', components: ['二', '厶'], story: 'Two private = cloud', level: 3, strokes: 4 },
  { id: 232, character: '太阳', pinyin: 'tàiyáng', meaning: 'sun', components: ['太', '阳'], story: 'Great yang = sun', level: 3, strokes: 7 },
  { id: 233, character: '月亮', pinyin: 'yuèliàng', meaning: 'moon', components: ['月', '亮'], story: 'Moon bright = moon', level: 3, strokes: 13 },
  { id: 234, character: '星星', pinyin: 'xīngxīng', meaning: 'star', components: ['星', '星'], story: 'Star star = stars', level: 3, strokes: 18 },
  { id: 235, character: '山', pinyin: 'shān', meaning: 'mountain', components: ['山'], story: 'Three peaks = mountain', level: 3, strokes: 3 },
  { id: 236, character: '河', pinyin: 'hé', meaning: 'river', components: ['氵', '可'], story: 'Water can = river', level: 3, strokes: 8 },
  { id: 237, character: '湖', pinyin: 'hú', meaning: 'lake', components: ['氵', '胡'], story: 'Water beard = lake', level: 3, strokes: 12 },
  { id: 238, character: '海', pinyin: 'hǎi', meaning: 'sea', components: ['氵', '每'], story: 'Water every = sea', level: 3, strokes: 10 },
  { id: 239, character: '树', pinyin: 'shù', meaning: 'tree', components: ['木', '对'], story: 'Wood pair = tree', level: 3, strokes: 9 },
  { id: 240, character: '草', pinyin: 'cǎo', meaning: 'grass', components: ['艹', '早'], story: 'Plant early = grass', level: 3, strokes: 9 },
  { id: 241, character: '花', pinyin: 'huā', meaning: 'flower', components: ['艹', '化'], story: 'Plant change = flower', level: 3, strokes: 7 },
  { id: 242, character: '鸟', pinyin: 'niǎo', meaning: 'bird', components: ['鸟'], story: 'Bird with tail feathers', level: 3, strokes: 5 },
  { id: 243, character: '鱼', pinyin: 'yú', meaning: 'fish', components: ['鱼'], story: 'Fish with scales and tail', level: 3, strokes: 8 },
  { id: 244, character: '狗', pinyin: 'gǒu', meaning: 'dog', components: ['犭', '句'], story: 'Animal sentence = dog', level: 3, strokes: 8 },
  { id: 245, character: '猫', pinyin: 'māo', meaning: 'cat', components: ['犭', '苗'], story: 'Animal sprout = cat', level: 3, strokes: 11 },
  { id: 246, character: '马', pinyin: 'mǎ', meaning: 'horse', components: ['马'], story: 'Horse with mane and legs', level: 3, strokes: 3 },
  { id: 247, character: '牛', pinyin: 'niú', meaning: 'cow', components: ['牛'], story: 'Cow with horns', level: 3, strokes: 4 },
  { id: 248, character: '羊', pinyin: 'yáng', meaning: 'sheep', components: ['羊'], story: 'Sheep with horns', level: 3, strokes: 6 },
  { id: 249, character: '猪', pinyin: 'zhū', meaning: 'pig', components: ['犭', '者'], story: 'Animal person = pig', level: 3, strokes: 11 },
  { id: 250, character: '鸡', pinyin: 'jī', meaning: 'chicken', components: ['鸟', '奚'], story: 'Bird servant = chicken', level: 3, strokes: 7 },
  { id: 251, character: '蛋', pinyin: 'dàn', meaning: 'egg', components: ['虫', '疋'], story: 'Insect foot = egg', level: 3, strokes: 11 },
  { id: 252, character: '肉', pinyin: 'ròu', meaning: 'meat', components: ['肉'], story: 'Flesh inside ribs', level: 3, strokes: 6 },
  { id: 253, character: '菜', pinyin: 'cài', meaning: 'vegetable', components: ['艹', '采'], story: 'Plant pick = vegetable', level: 3, strokes: 11 },
  { id: 254, character: '米', pinyin: 'mǐ', meaning: 'rice', components: ['米'], story: 'Rice grains scattered', level: 3, strokes: 6 },
  { id: 255, character: '面', pinyin: 'miàn', meaning: 'noodles', components: ['面'], story: 'Face surface = noodles', level: 3, strokes: 9 },
  { id: 256, character: '包', pinyin: 'bāo', meaning: 'bag, bun', components: ['勹', '己'], story: 'Wrap self = bag', level: 3, strokes: 5 },
  { id: 257, character: '饺子', pinyin: 'jiǎozi', meaning: 'dumpling', components: ['饣', '交', '子'], story: 'Food exchange child = dumpling', level: 3, strokes: 14 },
  { id: 258, character: '茶', pinyin: 'chá', meaning: 'tea', components: ['艹', '人', '木'], story: 'Plant person wood = tea', level: 3, strokes: 9 },
  { id: 259, character: '咖啡', pinyin: 'kāfēi', meaning: 'coffee', components: ['口', '加', '口', '非'], story: 'Mouth add mouth not = coffee', level: 3, strokes: 17 },
  { id: 260, character: '牛奶', pinyin: 'niúnǎi', meaning: 'milk', components: ['牛', '奶'], story: 'Cow milk = milk', level: 3, strokes: 9 },
  { id: 261, character: '果汁', pinyin: 'guǒzhī', meaning: 'juice', components: ['果', '汁'], story: 'Fruit juice = juice', level: 3, strokes: 13 },
  { id: 262, character: '啤酒', pinyin: 'píjiǔ', meaning: 'beer', components: ['口', '卑', '酉', '九'], story: 'Mouth humble wine nine = beer', level: 3, strokes: 21 },
  { id: 263, character: '红酒', pinyin: 'hóngjiǔ', meaning: 'wine', components: ['红', '酒'], story: 'Red wine = wine', level: 3, strokes: 16 },
  { id: 264, character: '糖', pinyin: 'táng', meaning: 'sugar', components: ['米', '唐'], story: 'Rice Tang = sugar', level: 3, strokes: 16 },
  { id: 265, character: '盐', pinyin: 'yán', meaning: 'salt', components: ['臣', '卤'], story: 'Minister brine = salt', level: 3, strokes: 10 },
  { id: 266, character: '醋', pinyin: 'cù', meaning: 'vinegar', components: ['酉', '昔'], story: 'Wine past = vinegar', level: 3, strokes: 15 },
  { id: 267, character: '油', pinyin: 'yóu', meaning: 'oil', components: ['氵', '由'], story: 'Water reason = oil', level: 3, strokes: 8 },
  { id: 268, character: '辣', pinyin: 'là', meaning: 'spicy', components: ['辛', '束'], story: 'Bitter bundle = spicy', level: 3, strokes: 14 },
  { id: 269, character: '甜', pinyin: 'tián', meaning: 'sweet', components: ['甘', '舌'], story: 'Sweet tongue = sweet', level: 3, strokes: 11 },
  { id: 270, character: '酸', pinyin: 'suān', meaning: 'sour', components: ['酉', '夋'], story: 'Wine hurry = sour', level: 3, strokes: 14 },
  { id: 271, character: '苦', pinyin: 'kǔ', meaning: 'bitter', components: ['艹', '古'], story: 'Plant ancient = bitter', level: 3, strokes: 8 },
  { id: 272, character: '香', pinyin: 'xiāng', meaning: 'fragrant', components: ['禾', '日'], story: 'Grain sun = fragrant', level: 3, strokes: 9 },
  { id: 273, character: '臭', pinyin: 'chòu', meaning: 'smelly', components: ['自', '犬'], story: 'Self dog = smelly', level: 3, strokes: 10 },
  { id: 274, character: '新鲜', pinyin: 'xīnxiān', meaning: 'fresh', components: ['新', '鲜'], story: 'New fresh = fresh', level: 3, strokes: 26 },
  { id: 275, character: '干净', pinyin: 'gānjìng', meaning: 'clean', components: ['干', '净'], story: 'Dry clean = clean', level: 3, strokes: 14 },
  { id: 276, character: '脏', pinyin: 'zāng', meaning: 'dirty', components: ['月', '庄'], story: 'Flesh village = dirty', level: 3, strokes: 10 },
  { id: 277, character: '白', pinyin: 'bái', meaning: 'white', components: ['白'], story: 'White', level: 3, strokes: 4 },
  { id: 278, character: '黑', pinyin: 'hēi', meaning: 'black', components: ['黑'], story: 'Black', level: 3, strokes: 4 },
  { id: 279, character: '红', pinyin: 'hóng', meaning: 'red', components: ['红'], story: 'Red', level: 3, strokes: 4 },
  { id: 280, character: '黄', pinyin: 'huáng', meaning: 'yellow', components: ['黄'], story: 'Yellow', level: 3, strokes: 4 },
  { id: 281, character: '蓝', pinyin: 'lán', meaning: 'blue', components: ['蓝'], story: 'Blue', level: 3, strokes: 4 },
  { id: 282, character: '绿', pinyin: 'lǜ', meaning: 'green', components: ['绿'], story: 'Green', level: 3, strokes: 4 },
  { id: 283, character: '紫', pinyin: 'zǐ', meaning: 'purple', components: ['紫'], story: 'Purple', level: 3, strokes: 4 },
  { id: 284, character: '灰', pinyin: 'huī', meaning: 'gray', components: ['灰'], story: 'Gray', level: 3, strokes: 4 },
  { id: 285, character: '粉', pinyin: 'fěn', meaning: 'powder', components: ['粉'], story: 'Powder', level: 3, strokes: 4 },
  { id: 286, character: '沙', pinyin: 'shā', meaning: 'sand', components: ['沙'], story: 'Sand', level: 3, strokes: 4 },
  { id: 287, character: '土', pinyin: 'tǔ', meaning: 'earth', components: ['土'], story: 'Earth', level: 3, strokes: 4 },
  { id: 288, character: '石', pinyin: 'shí', meaning: 'stone', components: ['石'], story: 'Stone', level: 3, strokes: 4 },
  { id: 289, character: '木', pinyin: 'mù', meaning: 'wood', components: ['木'], story: 'Wood', level: 3, strokes: 4 },
  { id: 290, character: '竹', pinyin: 'zhú', meaning: 'bamboo', components: ['竹'], story: 'Bamboo', level: 3, strokes: 4 },
  { id: 291, character: '草', pinyin: 'cǎo', meaning: 'grass', components: ['艹'], story: 'Grass', level: 3, strokes: 4 },
  { id: 292, character: '花', pinyin: 'huā', meaning: 'flower', components: ['艹', '花'], story: 'Flower', level: 3, strokes: 5 },
  { id: 293, character: '果', pinyin: 'guǒ', meaning: 'fruit', components: ['果'], story: 'Fruit', level: 3, strokes: 4 },
  { id: 294, character: '菜', pinyin: 'cài', meaning: 'vegetable', components: ['艹', '菜'], story: 'Vegetable', level: 3, strokes: 5 },
  { id: 295, character: '鱼', pinyin: 'yú', meaning: 'fish', components: ['鱼'], story: 'Fish', level: 3, strokes: 4 },
  { id: 296, character: '鸟', pinyin: 'niǎo', meaning: 'bird', components: ['鸟'], story: 'Bird', level: 3, strokes: 4 },
  { id: 297, character: '虫', pinyin: 'chóng', meaning: 'insect', components: ['虫'], story: 'Insect', level: 3, strokes: 4 },
  { id: 298, character: '兽', pinyin: 'shòu', meaning: 'beast', components: ['兽'], story: 'Beast', level: 3, strokes: 4 },
  { id: 299, character: '鸟', pinyin: 'niǎo', meaning: 'bird', components: ['鸟'], story: 'Bird', level: 3, strokes: 4 },
  { id: 300, character: '马', pinyin: 'mǎ', meaning: 'horse', components: ['马'], story: 'Horse', level: 3, strokes: 4 },

  // 继续HSK 3级 (301-400)
  { id: 301, character: '漂亮', pinyin: 'piàoliang', meaning: 'beautiful', components: ['漂', '亮'], story: 'Float bright = beautiful', level: 3, strokes: 29 },
  { id: 302, character: '丑', pinyin: 'chǒu', meaning: 'ugly', components: ['丑'], story: 'Twisted face = ugly', level: 3, strokes: 4 },
  { id: 303, character: '胖', pinyin: 'pàng', meaning: 'fat', components: ['月', '半'], story: 'Flesh half = fat', level: 3, strokes: 9 },
  { id: 304, character: '瘦', pinyin: 'shòu', meaning: 'thin', components: ['疒', '叟'], story: 'Sick old = thin', level: 3, strokes: 14 },
  { id: 305, character: '高', pinyin: 'gāo', meaning: 'tall', components: ['亠', '口', '冂', '口'], story: 'Tower with doors = tall', level: 3, strokes: 10 },
  { id: 306, character: '矮', pinyin: 'ǎi', meaning: 'short', components: ['矢', '委'], story: 'Arrow bend = short', level: 3, strokes: 13 },
  { id: 307, character: '年轻', pinyin: 'niánqīng', meaning: 'young', components: ['年', '轻'], story: 'Year light = young', level: 3, strokes: 20 },
  { id: 308, character: '老', pinyin: 'lǎo', meaning: 'old', components: ['老'], story: 'Person with bent back = old', level: 3, strokes: 6 },
  { id: 309, character: '聪明', pinyin: 'cōngming', meaning: 'smart', components: ['聪', '明'], story: 'Hear bright = smart', level: 3, strokes: 25 },
  { id: 310, character: '笨', pinyin: 'bèn', meaning: 'stupid', components: ['竹', '本'], story: 'Bamboo root = stupid', level: 3, strokes: 11 },
  { id: 311, character: '努力', pinyin: 'nǔlì', meaning: 'hardworking', components: ['努', '力'], story: 'Slave strength = hardworking', level: 3, strokes: 9 },
  { id: 312, character: '懒', pinyin: 'lǎn', meaning: 'lazy', components: ['忄', '赖'], story: 'Heart rely = lazy', level: 3, strokes: 16 },
  { id: 313, character: '勇敢', pinyin: 'yǒnggǎn', meaning: 'brave', components: ['勇', '敢'], story: 'Brave dare = brave', level: 3, strokes: 20 },
  { id: 314, character: '害怕', pinyin: 'hàipà', meaning: 'afraid', components: ['害', '怕'], story: 'Harm fear = afraid', level: 3, strokes: 19 },
  { id: 315, character: '高兴', pinyin: 'gāoxìng', meaning: 'happy', components: ['高', '兴'], story: 'High interest = happy', level: 3, strokes: 16 },
  { id: 316, character: '难过', pinyin: 'nánguò', meaning: 'sad', components: ['难', '过'], story: 'Difficult pass = sad', level: 3, strokes: 24 },
  { id: 317, character: '生气', pinyin: 'shēngqì', meaning: 'angry', components: ['生', '气'], story: 'Birth air = angry', level: 3, strokes: 9 },
  { id: 318, character: '紧张', pinyin: 'jǐnzhāng', meaning: 'nervous', components: ['紧', '张'], story: 'Tight stretch = nervous', level: 3, strokes: 18 },
  { id: 319, character: '放松', pinyin: 'fàngsōng', meaning: 'relaxed', components: ['放', '松'], story: 'Release pine = relaxed', level: 3, strokes: 16 },
  { id: 320, character: '累', pinyin: 'lèi', meaning: 'tired', components: ['田', '糸'], story: 'Field thread = tired', level: 3, strokes: 11 },
  { id: 321, character: '舒服', pinyin: 'shūfu', meaning: 'comfortable', components: ['舒', '服'], story: 'Stretch clothes = comfortable', level: 3, strokes: 21 },
  { id: 322, character: '健康', pinyin: 'jiànkāng', meaning: 'healthy', components: ['健', '康'], story: 'Strong healthy = healthy', level: 3, strokes: 21 },
  { id: 323, character: '生病', pinyin: 'shēngbìng', meaning: 'sick', components: ['生', '病'], story: 'Birth illness = sick', level: 3, strokes: 15 },
  { id: 324, character: '医院', pinyin: 'yīyuàn', meaning: 'hospital', components: ['医', '院'], story: 'Medicine courtyard = hospital', level: 3, strokes: 17 },

  // HSK 4级开始 (325-500)
  { id: 325, character: '安全', pinyin: 'ānquán', meaning: 'safe', components: ['安', '全'], story: 'Peace complete = safe', level: 4, strokes: 12 },
  { id: 326, character: '班', pinyin: 'bān', meaning: 'class', components: ['王', '王'], story: 'Two kings = class', level: 4, strokes: 10 },
  { id: 327, character: '搬', pinyin: 'bān', meaning: 'to move', components: ['扌', '般'], story: 'Hand general = move', level: 4, strokes: 13 },
  { id: 328, character: '办法', pinyin: 'bànfǎ', meaning: 'method', components: ['办', '法'], story: 'Handle law = method', level: 4, strokes: 12 },
  { id: 329, character: '帮助', pinyin: 'bāngzhù', meaning: 'help', components: ['帮', '助'], story: 'Gang assist = help', level: 4, strokes: 24 },
  { id: 330, character: '包括', pinyin: 'bāokuò', meaning: 'include', components: ['包', '括'], story: 'Wrap expand = include', level: 4, strokes: 14 },
  { id: 331, character: '保护', pinyin: 'bǎohù', meaning: 'protect', components: ['保', '护'], story: 'Preserve guard = protect', level: 4, strokes: 16 },
  { id: 332, character: '保证', pinyin: 'bǎozhèng', meaning: 'guarantee', components: ['保', '证'], story: 'Preserve prove = guarantee', level: 4, strokes: 16 },
  { id: 333, character: '报告', pinyin: 'bàogào', meaning: 'report', components: ['报', '告'], story: 'Report tell = report', level: 4, strokes: 14 },
  { id: 334, character: '报名', pinyin: 'bàomíng', meaning: 'register', components: ['报', '名'], story: 'Report name = register', level: 4, strokes: 13 },
  { id: 335, character: '抱歉', pinyin: 'bàoqiàn', meaning: 'sorry', components: ['抱', '歉'], story: 'Embrace lacking = sorry', level: 4, strokes: 19 },
  { id: 336, character: '背', pinyin: 'bèi', meaning: 'back', components: ['北', '月'], story: 'North flesh = back', level: 4, strokes: 9 },
  { id: 337, character: '比较', pinyin: 'bǐjiào', meaning: 'compare', components: ['比', '较'], story: 'Compare more = compare', level: 4, strokes: 17 },
  { id: 338, character: '比赛', pinyin: 'bǐsài', meaning: 'competition', components: ['比', '赛'], story: 'Compare race = competition', level: 4, strokes: 21 },
  { id: 339, character: '必须', pinyin: 'bìxū', meaning: 'must', components: ['必', '须'], story: 'Must need = must', level: 4, strokes: 17 },
  { id: 340, character: '变化', pinyin: 'biànhuà', meaning: 'change', components: ['变', '化'], story: 'Change transform = change', level: 4, strokes: 12 },
  { id: 341, character: '表示', pinyin: 'biǎoshì', meaning: 'express', components: ['表', '示'], story: 'Surface show = express', level: 4, strokes: 13 },
  { id: 342, character: '表演', pinyin: 'biǎoyǎn', meaning: 'perform', components: ['表', '演'], story: 'Surface act = perform', level: 4, strokes: 22 },
  { id: 343, character: '别人', pinyin: 'biérén', meaning: 'others', components: ['别', '人'], story: 'Separate person = others', level: 4, strokes: 9 },
  { id: 344, character: '宾馆', pinyin: 'bīnguǎn', meaning: 'hotel', components: ['宾', '馆'], story: 'Guest house = hotel', level: 4, strokes: 24 },

  // 继续HSK 4级 (345-500)
  { id: 345, character: '不但', pinyin: 'bùdàn', meaning: 'not only', components: ['不', '但'], story: 'Not only = not only', level: 4, strokes: 11 },
  { id: 346, character: '不过', pinyin: 'bùguò', meaning: 'however', components: ['不', '过'], story: 'Not pass = however', level: 4, strokes: 10 },
  { id: 347, character: '不仅', pinyin: 'bùjǐn', meaning: 'not only', components: ['不', '仅'], story: 'Not only = not only', level: 4, strokes: 17 },
  { id: 348, character: '材料', pinyin: 'cáiliào', meaning: 'material', components: ['材', '料'], story: 'Wood material = material', level: 4, strokes: 17 },
  { id: 349, character: '猜', pinyin: 'cāi', meaning: 'to guess', components: ['犭', '青'], story: 'Animal green = guess', level: 4, strokes: 11 },
  { id: 350, character: '参加', pinyin: 'cānjiā', meaning: 'to participate', components: ['参', '加'], story: 'Join add = participate', level: 4, strokes: 13 },
  { id: 351, character: '草', pinyin: 'cǎo', meaning: 'grass', components: ['艹', '早'], story: 'Plant early = grass', level: 4, strokes: 9 },
  { id: 352, character: '层', pinyin: 'céng', meaning: 'layer, floor', components: ['尸', '曾'], story: 'Corpse once = layer', level: 4, strokes: 7 },
  { id: 353, character: '差', pinyin: 'chà', meaning: 'poor, bad', components: ['羊', '工'], story: 'Sheep work = poor', level: 4, strokes: 9 },
  { id: 354, character: '产品', pinyin: 'chǎnpǐn', meaning: 'product', components: ['产', '品'], story: 'Produce goods = product', level: 4, strokes: 15 },
  { id: 355, character: '超过', pinyin: 'chāoguò', meaning: 'to exceed', components: ['超', '过'], story: 'Jump over = exceed', level: 4, strokes: 18 },
  { id: 356, character: '成功', pinyin: 'chénggōng', meaning: 'success', components: ['成', '功'], story: 'Become merit = success', level: 4, strokes: 11 },
  { id: 357, character: '成绩', pinyin: 'chéngjì', meaning: 'achievement', components: ['成', '绩'], story: 'Become record = achievement', level: 4, strokes: 17 },
  { id: 358, character: '成熟', pinyin: 'chéngshú', meaning: 'mature', components: ['成', '熟'], story: 'Become cooked = mature', level: 4, strokes: 19 },
  { id: 359, character: '城市', pinyin: 'chéngshì', meaning: 'city', components: ['城', '市'], story: 'Wall market = city', level: 4, strokes: 15 },
  { id: 360, character: '诚实', pinyin: 'chéngshí', meaning: 'honest', components: ['诚', '实'], story: 'Sincere real = honest', level: 4, strokes: 22 },
  { id: 361, character: '吃惊', pinyin: 'chījīng', meaning: 'surprised', components: ['吃', '惊'], story: 'Eat shock = surprised', level: 4, strokes: 17 },
  { id: 362, character: '重新', pinyin: 'chóngxīn', meaning: 'again, anew', components: ['重', '新'], story: 'Heavy new = again', level: 4, strokes: 22 },
  { id: 363, character: '抽烟', pinyin: 'chōuyān', meaning: 'to smoke', components: ['抽', '烟'], story: 'Draw smoke = smoke', level: 4, strokes: 18 },
  { id: 364, character: '出现', pinyin: 'chūxiàn', meaning: 'to appear', components: ['出', '现'], story: 'Come out present = appear', level: 4, strokes: 13 },
  { id: 365, character: '除了', pinyin: 'chúle', meaning: 'except', components: ['除', '了'], story: 'Remove completed = except', level: 4, strokes: 12 },
  { id: 366, character: '传统', pinyin: 'chuántǒng', meaning: 'tradition', components: ['传', '统'], story: 'Pass unite = tradition', level: 4, strokes: 15 },
  { id: 367, character: '春', pinyin: 'chūn', meaning: 'spring', components: ['日', '屯'], story: 'Sun sprouting = spring', level: 4, strokes: 9 },
  { id: 368, character: '词汇', pinyin: 'cíhuì', meaning: 'vocabulary', components: ['词', '汇'], story: 'Words gather = vocabulary', level: 4, strokes: 18 },
  { id: 369, character: '聪明', pinyin: 'cōngming', meaning: 'smart', components: ['聪', '明'], story: 'Hear bright = smart', level: 4, strokes: 25 },
  { id: 346, character: '打扫', pinyin: 'dǎsǎo', meaning: 'to clean', components: ['打', '扫'], story: 'Hit sweep = clean', level: 4, strokes: 17 },
  { id: 347, character: '打印', pinyin: 'dǎyìn', meaning: 'to print', components: ['打', '印'], story: 'Hit seal = print', level: 4, strokes: 11 },
  { id: 348, character: '大概', pinyin: 'dàgài', meaning: 'probably', components: ['大', '概'], story: 'Big general = probably', level: 4, strokes: 16 },
  { id: 349, character: '大夫', pinyin: 'dàifu', meaning: 'doctor', components: ['大', '夫'], story: 'Big man = doctor', level: 4, strokes: 7 },
  { id: 350, character: '代表', pinyin: 'dàibiǎo', meaning: 'representative', components: ['代', '表'], story: 'Replace surface = representative', level: 4, strokes: 13 },
  { id: 351, character: '担心', pinyin: 'dānxīn', meaning: 'to worry', components: ['担', '心'], story: 'Carry heart = worry', level: 4, strokes: 12 },
  { id: 352, character: '当时', pinyin: 'dāngshí', meaning: 'at that time', components: ['当', '时'], story: 'Should time = at that time', level: 4, strokes: 13 },
  { id: 353, character: '导游', pinyin: 'dǎoyóu', meaning: 'tour guide', components: ['导', '游'], story: 'Guide travel = tour guide', level: 4, strokes: 21 },
  { id: 354, character: '到处', pinyin: 'dàochù', meaning: 'everywhere', components: ['到', '处'], story: 'Arrive place = everywhere', level: 4, strokes: 13 },
  { id: 355, character: '道歉', pinyin: 'dàoqiàn', meaning: 'to apologize', components: ['道', '歉'], story: 'Way lacking = apologize', level: 4, strokes: 26 },
  { id: 356, character: '得意', pinyin: 'déyì', meaning: 'proud', components: ['得', '意'], story: 'Get meaning = proud', level: 4, strokes: 24 },
  { id: 357, character: '灯', pinyin: 'dēng', meaning: 'lamp', components: ['火', '丁'], story: 'Fire nail = lamp', level: 4, strokes: 6 },
  { id: 358, character: '登记', pinyin: 'dēngjì', meaning: 'to register', components: ['登', '记'], story: 'Climb record = register', level: 4, strokes: 17 },
  { id: 359, character: '等等', pinyin: 'děngděng', meaning: 'and so on', components: ['等', '等'], story: 'Wait wait = and so on', level: 4, strokes: 24 },
  { id: 360, character: '地址', pinyin: 'dìzhǐ', meaning: 'address', components: ['地', '址'], story: 'Ground site = address', level: 4, strokes: 13 },
  { id: 361, character: '调查', pinyin: 'diàochá', meaning: 'to investigate', components: ['调', '查'], story: 'Adjust check = investigate', level: 4, strokes: 19 },
  { id: 362, character: '丢', pinyin: 'diū', meaning: 'to lose', components: ['丿', '去'], story: 'Slash go = lose', level: 4, strokes: 6 },
  { id: 363, character: '动作', pinyin: 'dòngzuò', meaning: 'action', components: ['动', '作'], story: 'Move do = action', level: 4, strokes: 13 },
  { id: 364, character: '断', pinyin: 'duàn', meaning: 'to break', components: ['米', '斤'], story: 'Rice axe = break', level: 4, strokes: 11 },
  { id: 365, character: '对话', pinyin: 'duìhuà', meaning: 'dialogue', components: ['对', '话'], story: 'Correct speech = dialogue', level: 4, strokes: 13 },
  { id: 366, character: '对面', pinyin: 'duìmiàn', meaning: 'opposite', components: ['对', '面'], story: 'Correct face = opposite', level: 4, strokes: 14 },
  { id: 367, character: '对象', pinyin: 'duìxiàng', meaning: 'target, object', components: ['对', '象'], story: 'Correct elephant = target', level: 4, strokes: 17 },
  { id: 368, character: '儿童', pinyin: 'értóng', meaning: 'children', components: ['儿', '童'], story: 'Child child = children', level: 4, strokes: 14 },
  { id: 369, character: '发达', pinyin: 'fādá', meaning: 'developed', components: ['发', '达'], story: 'Send reach = developed', level: 4, strokes: 13 },
  { id: 370, character: '发展', pinyin: 'fāzhǎn', meaning: 'to develop', components: ['发', '展'], story: 'Send unfold = develop', level: 4, strokes: 15 },
  { id: 371, character: '法律', pinyin: 'fǎlǜ', meaning: 'law', components: ['法', '律'], story: 'Method rule = law', level: 4, strokes: 17 },
  { id: 372, character: '翻译', pinyin: 'fānyì', meaning: 'to translate', components: ['翻', '译'], story: 'Turn interpret = translate', level: 4, strokes: 38 },
  { id: 373, character: '反对', pinyin: 'fǎnduì', meaning: 'to oppose', components: ['反', '对'], story: 'Reverse correct = oppose', level: 4, strokes: 9 },
  { id: 374, character: '范围', pinyin: 'fànwéi', meaning: 'scope, range', components: ['范', '围'], story: 'Model surround = scope', level: 4, strokes: 19 },
  { id: 375, character: '方便', pinyin: 'fāngbiàn', meaning: 'convenient', components: ['方', '便'], story: 'Square convenient = convenient', level: 4, strokes: 13 },
  { id: 376, character: '方法', pinyin: 'fāngfǎ', meaning: 'method', components: ['方', '法'], story: 'Square law = method', level: 4, strokes: 12 },
  { id: 377, character: '方面', pinyin: 'fāngmiàn', meaning: 'aspect', components: ['方', '面'], story: 'Square face = aspect', level: 4, strokes: 13 },
  { id: 378, character: '方向', pinyin: 'fāngxiàng', meaning: 'direction', components: ['方', '向'], story: 'Square toward = direction', level: 4, strokes: 10 },
  { id: 379, character: '放心', pinyin: 'fàngxīn', meaning: 'to be at ease', components: ['放', '心'], story: 'Release heart = be at ease', level: 4, strokes: 12 },
  { id: 380, character: '非常', pinyin: 'fēicháng', meaning: 'very', components: ['非', '常'], story: 'Not normal = very', level: 4, strokes: 19 },
  { id: 381, character: '分别', pinyin: 'fēnbié', meaning: 'to separate', components: ['分', '别'], story: 'Divide separate = separate', level: 4, strokes: 11 },
  { id: 382, character: '分析', pinyin: 'fēnxī', meaning: 'to analyze', components: ['分', '析'], story: 'Divide analyze = analyze', level: 4, strokes: 12 },
  { id: 383, character: '丰富', pinyin: 'fēngfù', meaning: 'rich, abundant', components: ['丰', '富'], story: 'Abundant rich = rich', level: 4, strokes: 15 },
  { id: 384, character: '否则', pinyin: 'fǒuzé', meaning: 'otherwise', components: ['否', '则'], story: 'No then = otherwise', level: 4, strokes: 14 },
  { id: 385, character: '服务', pinyin: 'fúwù', meaning: 'service', components: ['服', '务'], story: 'Clothes affair = service', level: 4, strokes: 13 },
  { id: 386, character: '复习', pinyin: 'fùxí', meaning: 'to review', components: ['复', '习'], story: 'Return practice = review', level: 4, strokes: 20 },
  { id: 387, character: '复杂', pinyin: 'fùzá', meaning: 'complex', components: ['复', '杂'], story: 'Return mixed = complex', level: 4, strokes: 27 },
  { id: 388, character: '改变', pinyin: 'gǎibiàn', meaning: 'to change', components: ['改', '变'], story: 'Change change = change', level: 4, strokes: 15 },
  { id: 389, character: '干净', pinyin: 'gānjìng', meaning: 'clean', components: ['干', '净'], story: 'Dry clean = clean', level: 4, strokes: 14 },
  { id: 390, character: '感情', pinyin: 'gǎnqíng', meaning: 'emotion', components: ['感', '情'], story: 'Feel emotion = emotion', level: 4, strokes: 25 },
  { id: 391, character: '感兴趣', pinyin: 'gǎnxìngqù', meaning: 'interested', components: ['感', '兴', '趣'], story: 'Feel interest fun = interested', level: 4, strokes: 28 },
  { id: 392, character: '刚才', pinyin: 'gāngcái', meaning: 'just now', components: ['刚', '才'], story: 'Just talent = just now', level: 4, strokes: 13 },
  { id: 393, character: '高级', pinyin: 'gāojí', meaning: 'high level', components: ['高', '级'], story: 'High level = high level', level: 4, strokes: 16 },
  { id: 394, character: '搞', pinyin: 'gǎo', meaning: 'to do, make', components: ['扌', '高'], story: 'Hand high = do', level: 4, strokes: 13 },
  { id: 395, character: '告诉', pinyin: 'gàosu', meaning: 'to tell', components: ['告', '诉'], story: 'Tell complain = tell', level: 4, strokes: 19 },
  { id: 396, character: '格外', pinyin: 'géwài', meaning: 'especially', components: ['格', '外'], story: 'Pattern outside = especially', level: 4, strokes: 15 },
  { id: 397, character: '根据', pinyin: 'gēnjù', meaning: 'according to', components: ['根', '据'], story: 'Root according = according to', level: 4, strokes: 22 },
  { id: 398, character: '更加', pinyin: 'gèngjiā', meaning: 'even more', components: ['更', '加'], story: 'More add = even more', level: 4, strokes: 12 },
  { id: 399, character: '工厂', pinyin: 'gōngchǎng', meaning: 'factory', components: ['工', '厂'], story: 'Work cliff = factory', level: 4, strokes: 5 },
  { id: 400, character: '工程师', pinyin: 'gōngchéngshī', meaning: 'engineer', components: ['工', '程', '师'], story: 'Work process teacher = engineer', level: 4, strokes: 21 },

  // 继续HSK 4级 (401-500)
  { id: 401, character: '公司', pinyin: 'gōngsī', meaning: 'company', components: ['公', '司'], story: 'Public manage = company', level: 4, strokes: 9 },
  { id: 402, character: '功课', pinyin: 'gōngkè', meaning: 'homework', components: ['功', '课'], story: 'Merit lesson = homework', level: 4, strokes: 19 },
  { id: 403, character: '沟通', pinyin: 'gōutōng', meaning: 'communicate', components: ['沟', '通'], story: 'Ditch through = communicate', level: 4, strokes: 17 },
  { id: 404, character: '购物', pinyin: 'gòuwù', meaning: 'shopping', components: ['购', '物'], story: 'Buy things = shopping', level: 4, strokes: 20 },
  { id: 405, character: '估计', pinyin: 'gūjì', meaning: 'estimate', components: ['估', '计'], story: 'Estimate plan = estimate', level: 4, strokes: 16 },
  { id: 406, character: '鼓励', pinyin: 'gǔlì', meaning: 'encourage', components: ['鼓', '励'], story: 'Drum encourage = encourage', level: 4, strokes: 20 },
  { id: 407, character: '挂', pinyin: 'guà', meaning: 'to hang', components: ['扌', '卦'], story: 'Hand divination = hang', level: 4, strokes: 9 },
  { id: 408, character: '关键', pinyin: 'guānjiàn', meaning: 'key, crucial', components: ['关', '键'], story: 'Close key = crucial', level: 4, strokes: 23 },
  { id: 409, character: '观众', pinyin: 'guānzhòng', meaning: 'audience', components: ['观', '众'], story: 'Watch crowd = audience', level: 4, strokes: 17 },
  { id: 410, character: '管理', pinyin: 'guǎnlǐ', meaning: 'manage', components: ['管', '理'], story: 'Tube reason = manage', level: 4, strokes: 25 },
  { id: 411, character: '光', pinyin: 'guāng', meaning: 'light', components: ['儿', '火'], story: 'Child fire = light', level: 4, strokes: 6 },
  { id: 412, character: '广告', pinyin: 'guǎnggào', meaning: 'advertisement', components: ['广', '告'], story: 'Wide tell = advertisement', level: 4, strokes: 10 },
  { id: 413, character: '规定', pinyin: 'guīdìng', meaning: 'regulation', components: ['规', '定'], story: 'Rule fix = regulation', level: 4, strokes: 19 },
  { id: 414, character: '国际', pinyin: 'guójì', meaning: 'international', components: ['国', '际'], story: 'Country border = international', level: 4, strokes: 22 },
  { id: 415, character: '果然', pinyin: 'guǒrán', meaning: 'as expected', components: ['果', '然'], story: 'Fruit so = as expected', level: 4, strokes: 20 },
  { id: 416, character: '过程', pinyin: 'guòchéng', meaning: 'process', components: ['过', '程'], story: 'Pass journey = process', level: 4, strokes: 25 },
  { id: 417, character: '害怕', pinyin: 'hàipà', meaning: 'afraid', components: ['害', '怕'], story: 'Harm fear = afraid', level: 4, strokes: 19 },
  { id: 418, character: '海洋', pinyin: 'hǎiyáng', meaning: 'ocean', components: ['海', '洋'], story: 'Sea ocean = ocean', level: 4, strokes: 20 },
  { id: 419, character: '含', pinyin: 'hán', meaning: 'contain', components: ['今', '口'], story: 'Now mouth = contain', level: 4, strokes: 7 },
  { id: 420, character: '寒假', pinyin: 'hánjià', meaning: 'winter vacation', components: ['寒', '假'], story: 'Cold fake = winter vacation', level: 4, strokes: 23 },
  { id: 421, character: '行业', pinyin: 'hángyè', meaning: 'industry', components: ['行', '业'], story: 'Walk business = industry', level: 4, strokes: 19 },
  { id: 422, character: '好处', pinyin: 'hǎochù', meaning: 'benefit', components: ['好', '处'], story: 'Good place = benefit', level: 4, strokes: 11 },
  { id: 423, character: '合适', pinyin: 'héshì', meaning: 'suitable', components: ['合', '适'], story: 'Combine suitable = suitable', level: 4, strokes: 15 },
  { id: 424, character: '和平', pinyin: 'hépíng', meaning: 'peace', components: ['和', '平'], story: 'Harmony flat = peace', level: 4, strokes: 13 },
  { id: 425, character: '后悔', pinyin: 'hòuhuǐ', meaning: 'regret', components: ['后', '悔'], story: 'After regret = regret', level: 4, strokes: 18 },
  { id: 426, character: '厚', pinyin: 'hòu', meaning: 'thick', components: ['厂', '日', '子'], story: 'Cliff sun child = thick', level: 4, strokes: 9 },
  { id: 427, character: '护照', pinyin: 'hùzhào', meaning: 'passport', components: ['护', '照'], story: 'Protect shine = passport', level: 4, strokes: 20 },
  { id: 428, character: '花费', pinyin: 'huāfèi', meaning: 'cost', components: ['花', '费'], story: 'Flower cost = cost', level: 4, strokes: 19 },
  { id: 429, character: '划', pinyin: 'huá', meaning: 'to row', components: ['扌', '戈'], story: 'Hand weapon = row', level: 4, strokes: 6 },
  { id: 430, character: '化学', pinyin: 'huàxué', meaning: 'chemistry', components: ['化', '学'], story: 'Change study = chemistry', level: 4, strokes: 12 },
  { id: 431, character: '怀疑', pinyin: 'huáiyí', meaning: 'doubt', components: ['怀', '疑'], story: 'Embrace doubt = doubt', level: 4, strokes: 21 },
  { id: 432, character: '环境', pinyin: 'huánjìng', meaning: 'environment', components: ['环', '境'], story: 'Ring boundary = environment', level: 4, strokes: 22 },
  { id: 433, character: '换', pinyin: 'huàn', meaning: 'to change', components: ['扌', '奂'], story: 'Hand exchange = change', level: 4, strokes: 10 },
  { id: 434, character: '黄', pinyin: 'huáng', meaning: 'yellow', components: ['艹', '由', '八'], story: 'Grass reason eight = yellow', level: 4, strokes: 11 },
  { id: 435, character: '回忆', pinyin: 'huíyì', meaning: 'memory', components: ['回', '忆'], story: 'Return remember = memory', level: 4, strokes: 10 },
  { id: 436, character: '婚姻', pinyin: 'hūnyīn', meaning: 'marriage', components: ['婚', '姻'], story: 'Marriage marriage = marriage', level: 4, strokes: 20 },
  { id: 437, character: '活跃', pinyin: 'huóyuè', meaning: 'active', components: ['活', '跃'], story: 'Live jump = active', level: 4, strokes: 30 },
  { id: 438, character: '火车站', pinyin: 'huǒchēzhàn', meaning: 'train station', components: ['火', '车', '站'], story: 'Fire car stand = train station', level: 4, strokes: 19 },
  { id: 439, character: '获得', pinyin: 'huòdé', meaning: 'obtain', components: ['获', '得'], story: 'Capture get = obtain', level: 4, strokes: 22 },
  { id: 440, character: '机会', pinyin: 'jīhuì', meaning: 'opportunity', components: ['机', '会'], story: 'Machine meet = opportunity', level: 4, strokes: 12 },
  { id: 441, character: '积极', pinyin: 'jījí', meaning: 'positive', components: ['积', '极'], story: 'Accumulate extreme = positive', level: 4, strokes: 19 },
  { id: 442, character: '基础', pinyin: 'jīchǔ', meaning: 'foundation', components: ['基', '础'], story: 'Base foundation = foundation', level: 4, strokes: 23 },
  { id: 443, character: '激动', pinyin: 'jīdòng', meaning: 'excited', components: ['激', '动'], story: 'Stimulate move = excited', level: 4, strokes: 22 },
  { id: 444, character: '及时', pinyin: 'jíshí', meaning: 'timely', components: ['及', '时'], story: 'Reach time = timely', level: 4, strokes: 10 },
  { id: 445, character: '即使', pinyin: 'jíshǐ', meaning: 'even if', components: ['即', '使'], story: 'Immediate use = even if', level: 4, strokes: 19 },
  { id: 446, character: '继续', pinyin: 'jìxù', meaning: 'continue', components: ['继', '续'], story: 'Inherit continue = continue', level: 4, strokes: 31 },
  { id: 447, character: '家具', pinyin: 'jiājù', meaning: 'furniture', components: ['家', '具'], story: 'Home tools = furniture', level: 4, strokes: 18 },
  { id: 448, character: '假如', pinyin: 'jiǎrú', meaning: 'if', components: ['假', '如'], story: 'False like = if', level: 4, strokes: 17 },
  { id: 449, character: '价格', pinyin: 'jiàgé', meaning: 'price', components: ['价', '格'], story: 'Value pattern = price', level: 4, strokes: 14 },
  { id: 450, character: '坚持', pinyin: 'jiānchí', meaning: 'persist', components: ['坚', '持'], story: 'Firm hold = persist', level: 4, strokes: 18 },
  { id: 451, character: '减少', pinyin: 'jiǎnshǎo', meaning: 'reduce', components: ['减', '少'], story: 'Subtract few = reduce', level: 4, strokes: 23 },
  { id: 452, character: '简单', pinyin: 'jiǎndān', meaning: 'simple', components: ['简', '单'], story: 'Simple single = simple', level: 4, strokes: 31 },
  { id: 453, character: '建议', pinyin: 'jiànyì', meaning: 'suggest', components: ['建', '议'], story: 'Build discuss = suggest', level: 4, strokes: 28 },
  { id: 454, character: '健康', pinyin: 'jiànkāng', meaning: 'healthy', components: ['健', '康'], story: 'Strong healthy = healthy', level: 4, strokes: 21 },
  { id: 455, character: '讲', pinyin: 'jiǎng', meaning: 'speak', components: ['讠', '奖'], story: 'Words prize = speak', level: 4, strokes: 6 },
  { id: 456, character: '交流', pinyin: 'jiāoliú', meaning: 'exchange', components: ['交', '流'], story: 'Cross flow = exchange', level: 4, strokes: 16 },
  { id: 457, character: '交通', pinyin: 'jiāotōng', meaning: 'traffic', components: ['交', '通'], story: 'Cross through = traffic', level: 4, strokes: 16 },
  { id: 458, character: '骄傲', pinyin: 'jiāo\'ào', meaning: 'proud', components: ['骄', '傲'], story: 'Horse proud = proud', level: 4, strokes: 25 },
  { id: 459, character: '角度', pinyin: 'jiǎodù', meaning: 'angle', components: ['角', '度'], story: 'Horn degree = angle', level: 4, strokes: 16 },
  { id: 460, character: '教育', pinyin: 'jiàoyù', meaning: 'education', components: ['教', '育'], story: 'Teach nurture = education', level: 4, strokes: 19 },
  { id: 461, character: '接受', pinyin: 'jiēshòu', meaning: 'accept', components: ['接', '受'], story: 'Connect receive = accept', level: 4, strokes: 19 },
  { id: 462, character: '节目', pinyin: 'jiémù', meaning: 'program', components: ['节', '目'], story: 'Festival eye = program', level: 4, strokes: 10 },
  { id: 463, character: '节约', pinyin: 'jiéyuē', meaning: 'save', components: ['节', '约'], story: 'Festival promise = save', level: 4, strokes: 14 },
  { id: 464, character: '结果', pinyin: 'jiéguǒ', meaning: 'result', components: ['结', '果'], story: 'Tie fruit = result', level: 4, strokes: 17 },
  { id: 465, character: '结合', pinyin: 'jiéhé', meaning: 'combine', components: ['结', '合'], story: 'Tie combine = combine', level: 4, strokes: 15 },
  { id: 466, character: '结婚', pinyin: 'jiéhūn', meaning: 'marry', components: ['结', '婚'], story: 'Tie marriage = marry', level: 4, strokes: 20 },
  { id: 467, character: '解决', pinyin: 'jiějué', meaning: 'solve', components: ['解', '决'], story: 'Untie decide = solve', level: 4, strokes: 20 },
  { id: 468, character: '解释', pinyin: 'jiěshì', meaning: 'explain', components: ['解', '释'], story: 'Untie release = explain', level: 4, strokes: 26 },
  { id: 469, character: '借', pinyin: 'jiè', meaning: 'borrow', components: ['亻', '昔'], story: 'Person past = borrow', level: 4, strokes: 10 },
  { id: 470, character: '金', pinyin: 'jīn', meaning: 'gold', components: ['人', '王'], story: 'Person king = gold', level: 4, strokes: 8 },
  { id: 471, character: '紧张', pinyin: 'jǐnzhāng', meaning: 'nervous', components: ['紧', '张'], story: 'Tight stretch = nervous', level: 4, strokes: 18 },
  { id: 472, character: '尽管', pinyin: 'jǐnguǎn', meaning: 'although', components: ['尽', '管'], story: 'Exhaust manage = although', level: 4, strokes: 20 },
  { id: 473, character: '进步', pinyin: 'jìnbù', meaning: 'progress', components: ['进', '步'], story: 'Enter step = progress', level: 4, strokes: 14 },
  { id: 474, character: '进行', pinyin: 'jìnxíng', meaning: 'carry out', components: ['进', '行'], story: 'Enter walk = carry out', level: 4, strokes: 13 },
  { id: 475, character: '经常', pinyin: 'jīngcháng', meaning: 'often', components: ['经', '常'], story: 'Pass normal = often', level: 4, strokes: 19 },
  { id: 476, character: '经济', pinyin: 'jīngjì', meaning: 'economy', components: ['经', '济'], story: 'Pass help = economy', level: 4, strokes: 22 },
  { id: 477, character: '经理', pinyin: 'jīnglǐ', meaning: 'manager', components: ['经', '理'], story: 'Pass reason = manager', level: 4, strokes: 19 },
  { id: 478, character: '经验', pinyin: 'jīngyàn', meaning: 'experience', components: ['经', '验'], story: 'Pass test = experience', level: 4, strokes: 21 },
  { id: 479, character: '精神', pinyin: 'jīngshén', meaning: 'spirit', components: ['精', '神'], story: 'Essence god = spirit', level: 4, strokes: 23 },
  { id: 480, character: '竞争', pinyin: 'jìngzhēng', meaning: 'compete', components: ['竞', '争'], story: 'Compete fight = compete', level: 4, strokes: 18 },
  { id: 481, character: '究竟', pinyin: 'jiūjìng', meaning: 'after all', components: ['究', '竟'], story: 'Research end = after all', level: 4, strokes: 16 },
  { id: 482, character: '举办', pinyin: 'jǔbàn', meaning: 'hold (event)', components: ['举', '办'], story: 'Lift handle = hold event', level: 4, strokes: 13 },
  { id: 483, character: '拒绝', pinyin: 'jùjué', meaning: 'refuse', components: ['拒', '绝'], story: 'Resist cut = refuse', level: 4, strokes: 18 },
  { id: 484, character: '距离', pinyin: 'jùlí', meaning: 'distance', components: ['距', '离'], story: 'Distance leave = distance', level: 4, strokes: 23 },
  { id: 485, character: '决定', pinyin: 'juédìng', meaning: 'decide', components: ['决', '定'], story: 'Decide fix = decide', level: 4, strokes: 15 },
  { id: 486, character: '觉得', pinyin: 'juéde', meaning: 'feel', components: ['觉', '得'], story: 'Feel get = feel', level: 4, strokes: 22 },
  { id: 487, character: '开始', pinyin: 'kāishǐ', meaning: 'begin', components: ['开', '始'], story: 'Open start = begin', level: 4, strokes: 13 },
  { id: 488, character: '开玩笑', pinyin: 'kāiwánxiào', meaning: 'joke', components: ['开', '玩', '笑'], story: 'Open play laugh = joke', level: 4, strokes: 23 },
  { id: 489, character: '考虑', pinyin: 'kǎolǜ', meaning: 'consider', components: ['考', '虑'], story: 'Test worry = consider', level: 4, strokes: 21 },
  { id: 490, character: '科学', pinyin: 'kēxué', meaning: 'science', components: ['科', '学'], story: 'Subject study = science', level: 4, strokes: 17 },
  { id: 491, character: '可怜', pinyin: 'kělián', meaning: 'pitiful', components: ['可', '怜'], story: 'Can pity = pitiful', level: 4, strokes: 13 },
  { id: 492, character: '可是', pinyin: 'kěshì', meaning: 'but', components: ['可', '是'], story: 'Can be = but', level: 4, strokes: 14 },
  { id: 493, character: '客人', pinyin: 'kèrén', meaning: 'guest', components: ['客', '人'], story: 'Guest person = guest', level: 4, strokes: 11 },
  { id: 494, character: '空气', pinyin: 'kōngqì', meaning: 'air', components: ['空', '气'], story: 'Empty air = air', level: 4, strokes: 12 },
  { id: 495, character: '控制', pinyin: 'kòngzhì', meaning: 'control', components: ['控', '制'], story: 'Control make = control', level: 4, strokes: 19 },
  { id: 496, character: '口味', pinyin: 'kǒuwèi', meaning: 'taste', components: ['口', '味'], story: 'Mouth taste = taste', level: 4, strokes: 11 },
  { id: 497, character: '哭', pinyin: 'kū', meaning: 'cry', components: ['口', '犬'], story: 'Mouth dog = cry', level: 4, strokes: 10 },
  { id: 498, character: '苦', pinyin: 'kǔ', meaning: 'bitter', components: ['艹', '古'], story: 'Plant ancient = bitter', level: 4, strokes: 8 },
  { id: 499, character: '夸', pinyin: 'kuā', meaning: 'praise', components: ['大', '亏'], story: 'Big loss = praise', level: 4, strokes: 6 },
  { id: 500, character: '快乐', pinyin: 'kuàilè', meaning: 'happy', components: ['快', '乐'], story: 'Fast joy = happy', level: 4, strokes: 22 },

  // HSK 5级开始 (501-800字)
  { id: 501, character: '爱情', pinyin: 'àiqíng', meaning: 'love', components: ['爱', '情'], story: 'Love emotion = love', level: 5, strokes: 25 },
  { id: 502, character: '安排', pinyin: 'ānpái', meaning: 'arrange', components: ['安', '排'], story: 'Peace arrange = arrange', level: 5, strokes: 17 },
  { id: 503, character: '安装', pinyin: 'ānzhuāng', meaning: 'install', components: ['安', '装'], story: 'Peace dress = install', level: 5, strokes: 19 },
  { id: 504, character: '岸', pinyin: 'àn', meaning: 'shore', components: ['山', '干'], story: 'Mountain dry = shore', level: 5, strokes: 8 },
  { id: 505, character: '把握', pinyin: 'bǎwò', meaning: 'grasp', components: ['把', '握'], story: 'Hold grasp = grasp', level: 5, strokes: 16 },
  { id: 506, character: '摆', pinyin: 'bǎi', meaning: 'place', components: ['扌', '罢'], story: 'Hand stop = place', level: 5, strokes: 13 },
  { id: 507, character: '败', pinyin: 'bài', meaning: 'defeat', components: ['贝', '攵'], story: 'Shell hit = defeat', level: 5, strokes: 8 },
  { id: 508, character: '拜访', pinyin: 'bàifǎng', meaning: 'visit', components: ['拜', '访'], story: 'Worship visit = visit', level: 5, strokes: 20 },
  { id: 509, character: '班主任', pinyin: 'bānzhǔrèn', meaning: 'class teacher', components: ['班', '主', '任'], story: 'Class master duty = class teacher', level: 5, strokes: 16 },
  { id: 510, character: '版本', pinyin: 'bǎnběn', meaning: 'version', components: ['版', '本'], story: 'Plate root = version', level: 5, strokes: 12 },

  // 继续HSK 5级 (511-650)
  { id: 511, character: '办法', pinyin: 'bànfǎ', meaning: 'method', components: ['办', '法'], story: 'Handle law = method', level: 5, strokes: 12 },
  { id: 512, character: '半途', pinyin: 'bàntú', meaning: 'halfway', components: ['半', '途'], story: 'Half road = halfway', level: 5, strokes: 15 },
  { id: 513, character: '包含', pinyin: 'bāohán', meaning: 'contain', components: ['包', '含'], story: 'Wrap contain = contain', level: 5, strokes: 12 },
  { id: 514, character: '包围', pinyin: 'bāowéi', meaning: 'surround', components: ['包', '围'], story: 'Wrap around = surround', level: 5, strokes: 16 },
  { id: 515, character: '保持', pinyin: 'bǎochí', meaning: 'maintain', components: ['保', '持'], story: 'Protect hold = maintain', level: 5, strokes: 18 },
  { id: 516, character: '保存', pinyin: 'bǎocún', meaning: 'save', components: ['保', '存'], story: 'Protect exist = save', level: 5, strokes: 15 },
  { id: 517, character: '保护', pinyin: 'bǎohù', meaning: 'protect', components: ['保', '护'], story: 'Protect guard = protect', level: 5, strokes: 16 },
  { id: 518, character: '保留', pinyin: 'bǎoliú', meaning: 'reserve', components: ['保', '留'], story: 'Protect stay = reserve', level: 5, strokes: 19 },
  { id: 519, character: '保证', pinyin: 'bǎozhèng', meaning: 'guarantee', components: ['保', '证'], story: 'Protect prove = guarantee', level: 5, strokes: 16 },
  { id: 520, character: '报告', pinyin: 'bàogào', meaning: 'report', components: ['报', '告'], story: 'Report tell = report', level: 5, strokes: 14 },
  { id: 521, character: '报名', pinyin: 'bàomíng', meaning: 'register', components: ['报', '名'], story: 'Report name = register', level: 5, strokes: 13 },
  { id: 522, character: '抱怨', pinyin: 'bàoyuàn', meaning: 'complain', components: ['抱', '怨'], story: 'Hold grudge = complain', level: 5, strokes: 16 },
  { id: 523, character: '背景', pinyin: 'bèijǐng', meaning: 'background', components: ['背', '景'], story: 'Back view = background', level: 5, strokes: 21 },
  { id: 524, character: '被子', pinyin: 'bèizi', meaning: 'quilt', components: ['被', '子'], story: 'Cover child = quilt', level: 5, strokes: 8 },
  { id: 525, character: '本来', pinyin: 'běnlái', meaning: 'originally', components: ['本', '来'], story: 'Root come = originally', level: 5, strokes: 12 },
  { id: 526, character: '本质', pinyin: 'běnzhì', meaning: 'essence', components: ['本', '质'], story: 'Root quality = essence', level: 5, strokes: 19 },
  { id: 527, character: '比较', pinyin: 'bǐjiào', meaning: 'compare', components: ['比', '较'], story: 'Compare more = compare', level: 5, strokes: 17 },
  { id: 528, character: '比如', pinyin: 'bǐrú', meaning: 'for example', components: ['比', '如'], story: 'Compare like = for example', level: 5, strokes: 10 },
  { id: 529, character: '必然', pinyin: 'bìrán', meaning: 'inevitable', components: ['必', '然'], story: 'Must so = inevitable', level: 5, strokes: 17 },
  { id: 530, character: '必要', pinyin: 'bìyào', meaning: 'necessary', components: ['必', '要'], story: 'Must want = necessary', level: 5, strokes: 14 },
  { id: 531, character: '避免', pinyin: 'bìmiǎn', meaning: 'avoid', components: ['避', '免'], story: 'Avoid free = avoid', level: 5, strokes: 23 },
  { id: 532, character: '编辑', pinyin: 'biānjí', meaning: 'edit', components: ['编', '辑'], story: 'Weave gather = edit', level: 5, strokes: 27 },
  { id: 533, character: '变化', pinyin: 'biànhuà', meaning: 'change', components: ['变', '化'], story: 'Change transform = change', level: 5, strokes: 16 },
  { id: 534, character: '标准', pinyin: 'biāozhǔn', meaning: 'standard', components: ['标', '准'], story: 'Mark accurate = standard', level: 5, strokes: 19 },
  { id: 535, character: '表达', pinyin: 'biǎodá', meaning: 'express', components: ['表', '达'], story: 'Surface reach = express', level: 5, strokes: 14 },
  { id: 536, character: '表面', pinyin: 'biǎomiàn', meaning: 'surface', components: ['表', '面'], story: 'Show face = surface', level: 5, strokes: 17 },
  { id: 537, character: '表示', pinyin: 'biǎoshì', meaning: 'indicate', components: ['表', '示'], story: 'Show display = indicate', level: 5, strokes: 13 },
  { id: 538, character: '表演', pinyin: 'biǎoyǎn', meaning: 'perform', components: ['表', '演'], story: 'Show act = perform', level: 5, strokes: 23 },
  { id: 539, character: '别人', pinyin: 'biérén', meaning: 'others', components: ['别', '人'], story: 'Other person = others', level: 5, strokes: 9 },
  { id: 540, character: '冰箱', pinyin: 'bīngxiāng', meaning: 'refrigerator', components: ['冰', '箱'], story: 'Ice box = refrigerator', level: 5, strokes: 21 },
  { id: 541, character: '并且', pinyin: 'bìngqiě', meaning: 'and', components: ['并', '且'], story: 'Together and = and', level: 5, strokes: 11 },
  { id: 542, character: '博物馆', pinyin: 'bówùguǎn', meaning: 'museum', components: ['博', '物', '馆'], story: 'Broad thing hall = museum', level: 5, strokes: 27 },
  { id: 543, character: '不但', pinyin: 'bùdàn', meaning: 'not only', components: ['不', '但'], story: 'Not only = not only', level: 5, strokes: 11 },
  { id: 544, character: '不过', pinyin: 'bùguò', meaning: 'however', components: ['不', '过'], story: 'Not pass = however', level: 5, strokes: 10 },
  { id: 545, character: '不仅', pinyin: 'bùjǐn', meaning: 'not only', components: ['不', '仅'], story: 'Not only = not only', level: 5, strokes: 15 },
  { id: 546, character: '不如', pinyin: 'bùrú', meaning: 'not as good as', components: ['不', '如'], story: 'Not like = not as good as', level: 5, strokes: 10 },
  { id: 547, character: '不足', pinyin: 'bùzú', meaning: 'insufficient', components: ['不', '足'], story: 'Not enough = insufficient', level: 5, strokes: 11 },
  { id: 548, character: '部分', pinyin: 'bùfèn', meaning: 'part', components: ['部', '分'], story: 'Department divide = part', level: 5, strokes: 14 },
  { id: 549, character: '部门', pinyin: 'bùmén', meaning: 'department', components: ['部', '门'], story: 'Part door = department', level: 5, strokes: 13 },
  { id: 550, character: '财产', pinyin: 'cáichǎn', meaning: 'property', components: ['财', '产'], story: 'Wealth produce = property', level: 5, strokes: 21 },
  { id: 551, character: '采取', pinyin: 'cǎiqǔ', meaning: 'adopt', components: ['采', '取'], story: 'Pick take = adopt', level: 5, strokes: 15 },
  { id: 552, character: '采用', pinyin: 'cǎiyòng', meaning: 'use', components: ['采', '用'], story: 'Pick use = use', level: 5, strokes: 13 },
  { id: 553, character: '参观', pinyin: 'cānguān', meaning: 'visit', components: ['参', '观'], story: 'Join look = visit', level: 5, strokes: 19 },
  { id: 554, character: '参加', pinyin: 'cānjiā', meaning: 'participate', components: ['参', '加'], story: 'Join add = participate', level: 5, strokes: 16 },
  { id: 555, character: '参考', pinyin: 'cānkǎo', meaning: 'reference', components: ['参', '考'], story: 'Join test = reference', level: 5, strokes: 17 },
  { id: 556, character: '操作', pinyin: 'cāozuò', meaning: 'operate', components: ['操', '作'], story: 'Handle make = operate', level: 5, strokes: 23 },
  { id: 557, character: '策略', pinyin: 'cèlüè', meaning: 'strategy', components: ['策', '略'], story: 'Plan brief = strategy', level: 5, strokes: 23 },
  { id: 558, character: '产品', pinyin: 'chǎnpǐn', meaning: 'product', components: ['产', '品'], story: 'Produce goods = product', level: 5, strokes: 20 },
  { id: 559, character: '产生', pinyin: 'chǎnshēng', meaning: 'produce', components: ['产', '生'], story: 'Birth life = produce', level: 5, strokes: 16 },
  { id: 560, character: '长期', pinyin: 'chángqī', meaning: 'long-term', components: ['长', '期'], story: 'Long period = long-term', level: 5, strokes: 16 },
  { id: 561, character: '尝试', pinyin: 'chángshì', meaning: 'try', components: ['尝', '试'], story: 'Taste test = try', level: 5, strokes: 27 },
  { id: 562, character: '超过', pinyin: 'chāoguò', meaning: 'exceed', components: ['超', '过'], story: 'Jump pass = exceed', level: 5, strokes: 18 },
  { id: 563, character: '成功', pinyin: 'chénggōng', meaning: 'success', components: ['成', '功'], story: 'Become work = success', level: 5, strokes: 11 },
  { id: 564, character: '成就', pinyin: 'chéngjiù', meaning: 'achievement', components: ['成', '就'], story: 'Become accomplish = achievement', level: 5, strokes: 18 },
  { id: 565, character: '成立', pinyin: 'chénglì', meaning: 'establish', components: ['成', '立'], story: 'Become stand = establish', level: 5, strokes: 11 },
  { id: 566, character: '成熟', pinyin: 'chéngshú', meaning: 'mature', components: ['成', '熟'], story: 'Become ripe = mature', level: 5, strokes: 19 },
  { id: 567, character: '承担', pinyin: 'chéngdān', meaning: 'undertake', components: ['承', '担'], story: 'Accept carry = undertake', level: 5, strokes: 16 },
  { id: 568, character: '承认', pinyin: 'chéngrèn', meaning: 'admit', components: ['承', '认'], story: 'Accept know = admit', level: 5, strokes: 12 },
  { id: 569, character: '城市', pinyin: 'chéngshì', meaning: 'city', components: ['城', '市'], story: 'Wall market = city', level: 5, strokes: 19 },
  { id: 570, character: '诚实', pinyin: 'chéngshí', meaning: 'honest', components: ['诚', '实'], story: 'Sincere real = honest', level: 5, strokes: 21 },
  { id: 571, character: '吃惊', pinyin: 'chījīng', meaning: 'surprised', components: ['吃', '惊'], story: 'Eat shock = surprised', level: 5, strokes: 17 },
  { id: 572, character: '重新', pinyin: 'chóngxīn', meaning: 'again', components: ['重', '新'], story: 'Heavy new = again', level: 5, strokes: 22 },
  { id: 573, character: '出版', pinyin: 'chūbǎn', meaning: 'publish', components: ['出', '版'], story: 'Out plate = publish', level: 5, strokes: 13 },
  { id: 574, character: '出口', pinyin: 'chūkǒu', meaning: 'export', components: ['出', '口'], story: 'Out mouth = export', level: 5, strokes: 8 },
  { id: 575, character: '出现', pinyin: 'chūxiàn', meaning: 'appear', components: ['出', '现'], story: 'Out present = appear', level: 5, strokes: 16 },
  { id: 576, character: '初步', pinyin: 'chūbù', meaning: 'preliminary', components: ['初', '步'], story: 'First step = preliminary', level: 5, strokes: 14 },
  { id: 577, character: '除了', pinyin: 'chúle', meaning: 'except', components: ['除', '了'], story: 'Remove finish = except', level: 5, strokes: 12 },
  { id: 578, character: '处理', pinyin: 'chǔlǐ', meaning: 'handle', components: ['处', '理'], story: 'Place reason = handle', level: 5, strokes: 13 },
  { id: 579, character: '传播', pinyin: 'chuánbō', meaning: 'spread', components: ['传', '播'], story: 'Pass sow = spread', level: 5, strokes: 28 },
  { id: 580, character: '传统', pinyin: 'chuántǒng', meaning: 'tradition', components: ['传', '统'], story: 'Pass unite = tradition', level: 5, strokes: 19 },
  { id: 581, character: '创造', pinyin: 'chuàngzào', meaning: 'create', components: ['创', '造'], story: 'Wound make = create', level: 5, strokes: 22 },
  { id: 582, character: '春天', pinyin: 'chūntiān', meaning: 'spring', components: ['春', '天'], story: 'Spring sky = spring', level: 5, strokes: 13 },
  { id: 583, character: '词汇', pinyin: 'cíhuì', meaning: 'vocabulary', components: ['词', '汇'], story: 'Word gather = vocabulary', level: 5, strokes: 26 },
  { id: 584, character: '此外', pinyin: 'cǐwài', meaning: 'besides', components: ['此', '外'], story: 'This outside = besides', level: 5, strokes: 11 },
  { id: 585, character: '从而', pinyin: 'cóngér', meaning: 'thus', components: ['从', '而'], story: 'Follow and = thus', level: 5, strokes: 10 },
  { id: 586, character: '从事', pinyin: 'cóngshì', meaning: 'engage in', components: ['从', '事'], story: 'Follow matter = engage in', level: 5, strokes: 12 },
  { id: 587, character: '促进', pinyin: 'cùjìn', meaning: 'promote', components: ['促', '进'], story: 'Urge advance = promote', level: 5, strokes: 16 },
  { id: 588, character: '存在', pinyin: 'cúnzài', meaning: 'exist', components: ['存', '在'], story: 'Store at = exist', level: 5, strokes: 12 },
  { id: 589, character: '错误', pinyin: 'cuòwù', meaning: 'mistake', components: ['错', '误'], story: 'Wrong error = mistake', level: 5, strokes: 27 },
  { id: 590, character: '达到', pinyin: 'dádào', meaning: 'reach', components: ['达', '到'], story: 'Reach arrive = reach', level: 5, strokes: 16 },
  { id: 591, character: '打击', pinyin: 'dǎjī', meaning: 'strike', components: ['打', '击'], story: 'Hit attack = strike', level: 5, strokes: 12 },
  { id: 592, character: '打算', pinyin: 'dǎsuàn', meaning: 'plan', components: ['打', '算'], story: 'Hit calculate = plan', level: 5, strokes: 18 },
  { id: 593, character: '大概', pinyin: 'dàgài', meaning: 'probably', components: ['大', '概'], story: 'Big general = probably', level: 5, strokes: 17 },
  { id: 594, character: '大量', pinyin: 'dàliàng', meaning: 'large amount', components: ['大', '量'], story: 'Big measure = large amount', level: 5, strokes: 15 },
  { id: 595, character: '大约', pinyin: 'dàyuē', meaning: 'approximately', components: ['大', '约'], story: 'Big agree = approximately', level: 5, strokes: 12 },
  { id: 596, character: '代表', pinyin: 'dàibiǎo', meaning: 'represent', components: ['代', '表'], story: 'Replace show = represent', level: 5, strokes: 13 },
  { id: 597, character: '代替', pinyin: 'dàitì', meaning: 'replace', components: ['代', '替'], story: 'Replace substitute = replace', level: 5, strokes: 17 },
  { id: 598, character: '带来', pinyin: 'dàilái', meaning: 'bring', components: ['带', '来'], story: 'Belt come = bring', level: 5, strokes: 16 },
  { id: 599, character: '单纯', pinyin: 'dānchún', meaning: 'simple', components: ['单', '纯'], story: 'Single pure = simple', level: 5, strokes: 15 },
  { id: 600, character: '单独', pinyin: 'dāndú', meaning: 'alone', components: ['单', '独'], story: 'Single alone = alone', level: 5, strokes: 17 },
  { id: 601, character: '单位', pinyin: 'dānwèi', meaning: 'unit', components: ['单', '位'], story: 'Single position = unit', level: 5, strokes: 12 },
  { id: 602, character: '担心', pinyin: 'dānxīn', meaning: 'worry', components: ['担', '心'], story: 'Carry heart = worry', level: 5, strokes: 12 },
  { id: 603, character: '当然', pinyin: 'dāngrán', meaning: 'of course', components: ['当', '然'], story: 'Should so = of course', level: 5, strokes: 18 },
  { id: 604, character: '当时', pinyin: 'dāngshí', meaning: 'at that time', components: ['当', '时'], story: 'Should time = at that time', level: 5, strokes: 16 },
  { id: 605, character: '导致', pinyin: 'dǎozhì', meaning: 'lead to', components: ['导', '致'], story: 'Guide reach = lead to', level: 5, strokes: 21 },
  { id: 606, character: '到处', pinyin: 'dàochù', meaning: 'everywhere', components: ['到', '处'], story: 'Arrive place = everywhere', level: 5, strokes: 13 },
  { id: 607, character: '到达', pinyin: 'dàodá', meaning: 'arrive', components: ['到', '达'], story: 'Arrive reach = arrive', level: 5, strokes: 16 },
  { id: 608, character: '得到', pinyin: 'dédào', meaning: 'get', components: ['得', '到'], story: 'Get arrive = get', level: 5, strokes: 19 },
  { id: 609, character: '的确', pinyin: 'díquè', meaning: 'indeed', components: ['的', '确'], story: 'Target sure = indeed', level: 5, strokes: 23 },
  { id: 610, character: '等等', pinyin: 'děngděng', meaning: 'and so on', components: ['等', '等'], story: 'Wait wait = and so on', level: 5, strokes: 24 },

  // 继续HSK 5级 (611-720)
  { id: 611, character: '地区', pinyin: 'dìqū', meaning: 'region', components: ['地', '区'], story: 'Earth area = region', level: 5, strokes: 13 },
  { id: 612, character: '地位', pinyin: 'dìwèi', meaning: 'status', components: ['地', '位'], story: 'Earth position = status', level: 5, strokes: 12 },
  { id: 613, character: '地址', pinyin: 'dìzhǐ', meaning: 'address', components: ['地', '址'], story: 'Earth site = address', level: 5, strokes: 12 },
  { id: 614, character: '第一', pinyin: 'dìyī', meaning: 'first', components: ['第', '一'], story: 'Order one = first', level: 5, strokes: 12 },
  { id: 615, character: '点心', pinyin: 'diǎnxīn', meaning: 'snack', components: ['点', '心'], story: 'Point heart = snack', level: 5, strokes: 13 },
  { id: 616, character: '电脑', pinyin: 'diànnǎo', meaning: 'computer', components: ['电', '脑'], story: 'Electric brain = computer', level: 5, strokes: 18 },
  { id: 617, character: '电视', pinyin: 'diànshì', meaning: 'television', components: ['电', '视'], story: 'Electric see = television', level: 5, strokes: 20 },
  { id: 618, character: '电影', pinyin: 'diànyǐng', meaning: 'movie', components: ['电', '影'], story: 'Electric shadow = movie', level: 5, strokes: 18 },
  { id: 619, character: '调查', pinyin: 'diàochá', meaning: 'investigate', components: ['调', '查'], story: 'Tune check = investigate', level: 5, strokes: 21 },
  { id: 620, character: '调整', pinyin: 'tiáozhěng', meaning: 'adjust', components: ['调', '整'], story: 'Tune correct = adjust', level: 5, strokes: 26 },
  { id: 621, character: '丢失', pinyin: 'diūshī', meaning: 'lose', components: ['丢', '失'], story: 'Throw lose = lose', level: 5, strokes: 10 },
  { id: 622, character: '动作', pinyin: 'dòngzuò', meaning: 'action', components: ['动', '作'], story: 'Move make = action', level: 5, strokes: 18 },
  { id: 623, character: '独立', pinyin: 'dúlì', meaning: 'independent', components: ['独', '立'], story: 'Alone stand = independent', level: 5, strokes: 17 },
  { id: 624, character: '独特', pinyin: 'dútè', meaning: 'unique', components: ['独', '特'], story: 'Alone special = unique', level: 5, strokes: 19 },
  { id: 625, character: '度过', pinyin: 'dùguò', meaning: 'spend time', components: ['度', '过'], story: 'Measure pass = spend time', level: 5, strokes: 18 },
  { id: 626, character: '断定', pinyin: 'duàndìng', meaning: 'conclude', components: ['断', '定'], story: 'Break decide = conclude', level: 5, strokes: 26 },
  { id: 627, character: '对比', pinyin: 'duìbǐ', meaning: 'contrast', components: ['对', '比'], story: 'Face compare = contrast', level: 5, strokes: 11 },
  { id: 628, character: '对待', pinyin: 'duìdài', meaning: 'treat', components: ['对', '待'], story: 'Face wait = treat', level: 5, strokes: 16 },
  { id: 629, character: '对方', pinyin: 'duìfāng', meaning: 'other party', components: ['对', '方'], story: 'Face direction = other party', level: 5, strokes: 11 },
  { id: 630, character: '对于', pinyin: 'duìyú', meaning: 'regarding', components: ['对', '于'], story: 'Face at = regarding', level: 5, strokes: 11 },
  { id: 631, character: '多么', pinyin: 'duōme', meaning: 'how', components: ['多', '么'], story: 'Many what = how', level: 5, strokes: 9 },
  { id: 632, character: '多数', pinyin: 'duōshù', meaning: 'majority', components: ['多', '数'], story: 'Many number = majority', level: 5, strokes: 19 },
  { id: 633, character: '多余', pinyin: 'duōyú', meaning: 'surplus', components: ['多', '余'], story: 'Many remain = surplus', level: 5, strokes: 14 },
  { id: 634, character: '发表', pinyin: 'fābiǎo', meaning: 'publish', components: ['发', '表'], story: 'Send surface = publish', level: 5, strokes: 13 },
  { id: 635, character: '发达', pinyin: 'fādá', meaning: 'developed', components: ['发', '达'], story: 'Send reach = developed', level: 5, strokes: 16 },
  { id: 636, character: '发挥', pinyin: 'fāhuī', meaning: 'play', components: ['发', '挥'], story: 'Send wave = play', level: 5, strokes: 17 },
  { id: 637, character: '发明', pinyin: 'fāmíng', meaning: 'invent', components: ['发', '明'], story: 'Send bright = invent', level: 5, strokes: 12 },
  { id: 638, character: '发生', pinyin: 'fāshēng', meaning: 'happen', components: ['发', '生'], story: 'Send life = happen', level: 5, strokes: 10 },
  { id: 639, character: '发展', pinyin: 'fāzhǎn', meaning: 'develop', components: ['发', '展'], story: 'Send unfold = develop', level: 5, strokes: 13 },
  { id: 640, character: '法律', pinyin: 'fǎlǜ', meaning: 'law', components: ['法', '律'], story: 'Method rule = law', level: 5, strokes: 17 },
  { id: 641, character: '翻译', pinyin: 'fānyì', meaning: 'translate', components: ['翻', '译'], story: 'Turn interpret = translate', level: 5, strokes: 38 },
  { id: 642, character: '反对', pinyin: 'fǎnduì', meaning: 'oppose', components: ['反', '对'], story: 'Reverse face = oppose', level: 5, strokes: 11 },
  { id: 643, character: '反映', pinyin: 'fǎnyìng', meaning: 'reflect', components: ['反', '映'], story: 'Reverse shine = reflect', level: 5, strokes: 12 },
  { id: 644, character: '反应', pinyin: 'fǎnyìng', meaning: 'reaction', components: ['反', '应'], story: 'Reverse respond = reaction', level: 5, strokes: 11 },
  { id: 645, character: '范围', pinyin: 'fànwéi', meaning: 'scope', components: ['范', '围'], story: 'Model surround = scope', level: 5, strokes: 19 },
  { id: 646, character: '方案', pinyin: 'fāng\'àn', meaning: 'plan', components: ['方', '案'], story: 'Direction case = plan', level: 5, strokes: 14 },
  { id: 647, character: '方式', pinyin: 'fāngshì', meaning: 'way', components: ['方', '式'], story: 'Direction style = way', level: 5, strokes: 13 },
  { id: 648, character: '方向', pinyin: 'fāngxiàng', meaning: 'direction', components: ['方', '向'], story: 'Square toward = direction', level: 5, strokes: 10 },
  { id: 649, character: '方面', pinyin: 'fāngmiàn', meaning: 'aspect', components: ['方', '面'], story: 'Direction face = aspect', level: 5, strokes: 13 },
  { id: 650, character: '防止', pinyin: 'fángzhǐ', meaning: 'prevent', components: ['防', '止'], story: 'Defend stop = prevent', level: 5, strokes: 14 },
  { id: 651, character: '非常', pinyin: 'fēicháng', meaning: 'very', components: ['非', '常'], story: 'Not normal = very', level: 5, strokes: 19 },
  { id: 652, character: '分别', pinyin: 'fēnbié', meaning: 'respectively', components: ['分', '别'], story: 'Divide other = respectively', level: 5, strokes: 14 },
  { id: 653, character: '分布', pinyin: 'fēnbù', meaning: 'distribute', components: ['分', '布'], story: 'Divide cloth = distribute', level: 5, strokes: 10 },
  { id: 654, character: '分析', pinyin: 'fēnxī', meaning: 'analyze', components: ['分', '析'], story: 'Divide analyze = analyze', level: 5, strokes: 12 },
  { id: 655, character: '丰富', pinyin: 'fēngfù', meaning: 'rich', components: ['丰', '富'], story: 'Abundant wealth = rich', level: 5, strokes: 20 },
  { id: 656, character: '风格', pinyin: 'fēnggé', meaning: 'style', components: ['风', '格'], story: 'Wind pattern = style', level: 5, strokes: 19 },
  { id: 657, character: '风景', pinyin: 'fēngjǐng', meaning: 'scenery', components: ['风', '景'], story: 'Wind view = scenery', level: 5, strokes: 21 },
  { id: 658, character: '风俗', pinyin: 'fēngsú', meaning: 'custom', components: ['风', '俗'], story: 'Wind common = custom', level: 5, strokes: 18 },
  { id: 659, character: '否定', pinyin: 'fǒudìng', meaning: 'deny', components: ['否', '定'], story: 'No decide = deny', level: 5, strokes: 15 },
  { id: 660, character: '否则', pinyin: 'fǒuzé', meaning: 'otherwise', components: ['否', '则'], story: 'No then = otherwise', level: 5, strokes: 16 },
  { id: 661, character: '符合', pinyin: 'fúhé', meaning: 'conform', components: ['符', '合'], story: 'Symbol join = conform', level: 5, strokes: 17 },
  { id: 662, character: '服务', pinyin: 'fúwù', meaning: 'service', components: ['服', '务'], story: 'Clothes affair = service', level: 5, strokes: 17 },
  { id: 663, character: '服装', pinyin: 'fúzhuāng', meaning: 'clothing', components: ['服', '装'], story: 'Clothes dress = clothing', level: 5, strokes: 21 },
  { id: 664, character: '辅导', pinyin: 'fǔdǎo', meaning: 'tutor', components: ['辅', '导'], story: 'Assist guide = tutor', level: 5, strokes: 21 },
  { id: 665, character: '复杂', pinyin: 'fùzá', meaning: 'complex', components: ['复', '杂'], story: 'Return mixed = complex', level: 5, strokes: 15 },
  { id: 666, character: '改变', pinyin: 'gǎibiàn', meaning: 'change', components: ['改', '变'], story: 'Change transform = change', level: 5, strokes: 15 },
  { id: 667, character: '改革', pinyin: 'gǎigé', meaning: 'reform', components: ['改', '革'], story: 'Change leather = reform', level: 5, strokes: 18 },
  { id: 668, character: '改进', pinyin: 'gǎijìn', meaning: 'improve', components: ['改', '进'], story: 'Change advance = improve', level: 5, strokes: 14 },
  { id: 669, character: '改善', pinyin: 'gǎishàn', meaning: 'improve', components: ['改', '善'], story: 'Change good = improve', level: 5, strokes: 19 },
  { id: 670, character: '概念', pinyin: 'gàiniàn', meaning: 'concept', components: ['概', '念'], story: 'General think = concept', level: 5, strokes: 21 },
  { id: 671, character: '干净', pinyin: 'gānjìng', meaning: 'clean', components: ['干', '净'], story: 'Dry pure = clean', level: 5, strokes: 13 },
  { id: 672, character: '干燥', pinyin: 'gānzào', meaning: 'dry', components: ['干', '燥'], story: 'Dry hot = dry', level: 5, strokes: 20 },
  { id: 673, character: '感情', pinyin: 'gǎnqíng', meaning: 'emotion', components: ['感', '情'], story: 'Feel emotion = emotion', level: 5, strokes: 25 },
  { id: 674, character: '感受', pinyin: 'gǎnshòu', meaning: 'feel', components: ['感', '受'], story: 'Feel receive = feel', level: 5, strokes: 21 },
  { id: 675, character: '感谢', pinyin: 'gǎnxiè', meaning: 'thank', components: ['感', '谢'], story: 'Feel thank = thank', level: 5, strokes: 30 },
  { id: 676, character: '刚才', pinyin: 'gāngcái', meaning: 'just now', components: ['刚', '才'], story: 'Just talent = just now', level: 5, strokes: 13 },
  { id: 677, character: '刚刚', pinyin: 'gānggāng', meaning: 'just', components: ['刚', '刚'], story: 'Just just = just', level: 5, strokes: 20 },
  { id: 678, character: '高级', pinyin: 'gāojí', meaning: 'advanced', components: ['高', '级'], story: 'High level = advanced', level: 5, strokes: 16 },
  { id: 679, character: '高兴', pinyin: 'gāoxìng', meaning: 'happy', components: ['高', '兴'], story: 'High interest = happy', level: 5, strokes: 16 },
  { id: 680, character: '搞', pinyin: 'gǎo', meaning: 'do', components: ['扌', '高'], story: 'Hand high = do', level: 5, strokes: 13 },
  { id: 681, character: '告诉', pinyin: 'gàosù', meaning: 'tell', components: ['告', '诉'], story: 'Announce speak = tell', level: 5, strokes: 14 },
  { id: 682, character: '格外', pinyin: 'géwài', meaning: 'especially', components: ['格', '外'], story: 'Pattern outside = especially', level: 5, strokes: 14 },
  { id: 683, character: '个别', pinyin: 'gèbié', meaning: 'individual', components: ['个', '别'], story: 'Individual other = individual', level: 5, strokes: 10 },
  { id: 684, character: '个人', pinyin: 'gèrén', meaning: 'personal', components: ['个', '人'], story: 'Individual person = personal', level: 5, strokes: 5 },
  { id: 685, character: '个性', pinyin: 'gèxìng', meaning: 'personality', components: ['个', '性'], story: 'Individual nature = personality', level: 5, strokes: 14 },
  { id: 686, character: '根本', pinyin: 'gēnběn', meaning: 'fundamental', components: ['根', '本'], story: 'Root origin = fundamental', level: 5, strokes: 15 },
  { id: 687, character: '根据', pinyin: 'gēnjù', meaning: 'according to', components: ['根', '据'], story: 'Root evidence = according to', level: 5, strokes: 21 },
  { id: 688, character: '更加', pinyin: 'gèngjiā', meaning: 'more', components: ['更', '加'], story: 'Change add = more', level: 5, strokes: 12 },
  { id: 689, character: '工程', pinyin: 'gōngchéng', meaning: 'engineering', components: ['工', '程'], story: 'Work journey = engineering', level: 5, strokes: 15 },
  { id: 690, character: '工厂', pinyin: 'gōngchǎng', meaning: 'factory', components: ['工', '厂'], story: 'Work factory = factory', level: 5, strokes: 7 },
  { id: 691, character: '工具', pinyin: 'gōngjù', meaning: 'tool', components: ['工', '具'], story: 'Work tool = tool', level: 5, strokes: 11 },
  { id: 692, character: '工人', pinyin: 'gōngrén', meaning: 'worker', components: ['工', '人'], story: 'Work person = worker', level: 5, strokes: 5 },
  { id: 693, character: '工业', pinyin: 'gōngyè', meaning: 'industry', components: ['工', '业'], story: 'Work business = industry', level: 5, strokes: 16 },
  { id: 694, character: '工作', pinyin: 'gōngzuò', meaning: 'work', components: ['工', '作'], story: 'Work make = work', level: 5, strokes: 10 },
  { id: 695, character: '功能', pinyin: 'gōngnéng', meaning: 'function', components: ['功', '能'], story: 'Merit ability = function', level: 5, strokes: 14 },
  { id: 696, character: '公布', pinyin: 'gōngbù', meaning: 'announce', components: ['公', '布'], story: 'Public cloth = announce', level: 5, strokes: 10 },
  { id: 697, character: '公开', pinyin: 'gōngkāi', meaning: 'public', components: ['公', '开'], story: 'Public open = public', level: 5, strokes: 8 },
  { id: 698, character: '公平', pinyin: 'gōngpíng', meaning: 'fair', components: ['公', '平'], story: 'Public level = fair', level: 5, strokes: 9 },
  { id: 699, character: '公司', pinyin: 'gōngsī', meaning: 'company', components: ['公', '司'], story: 'Public manage = company', level: 5, strokes: 10 },
  { id: 700, character: '公园', pinyin: 'gōngyuán', meaning: 'park', components: ['公', '园'], story: 'Public garden = park', level: 5, strokes: 12 },
  { id: 701, character: '公正', pinyin: 'gōngzhèng', meaning: 'just', components: ['公', '正'], story: 'Public correct = just', level: 5, strokes: 9 },
  { id: 702, character: '共同', pinyin: 'gòngtóng', meaning: 'common', components: ['共', '同'], story: 'Together same = common', level: 5, strokes: 12 },
  { id: 703, character: '沟通', pinyin: 'gōutōng', meaning: 'communicate', components: ['沟', '通'], story: 'Ditch through = communicate', level: 5, strokes: 20 },
  { id: 704, character: '构成', pinyin: 'gòuchéng', meaning: 'constitute', components: ['构', '成'], story: 'Structure become = constitute', level: 5, strokes: 15 },
  { id: 705, character: '姑娘', pinyin: 'gūniang', meaning: 'girl', components: ['姑', '娘'], story: 'Aunt lady = girl', level: 5, strokes: 18 },
  { id: 706, character: '古代', pinyin: 'gǔdài', meaning: 'ancient', components: ['古', '代'], story: 'Old generation = ancient', level: 5, strokes: 10 },
  { id: 707, character: '古典', pinyin: 'gǔdiǎn', meaning: 'classical', components: ['古', '典'], story: 'Old classic = classical', level: 5, strokes: 13 },
  { id: 708, character: '鼓励', pinyin: 'gǔlì', meaning: 'encourage', components: ['鼓', '励'], story: 'Drum encourage = encourage', level: 5, strokes: 20 },
  { id: 709, character: '固定', pinyin: 'gùdìng', meaning: 'fixed', components: ['固', '定'], story: 'Solid decide = fixed', level: 5, strokes: 16 },
  { id: 710, character: '关键', pinyin: 'guānjiàn', meaning: 'key', components: ['关', '键'], story: 'Close key = key', level: 5, strokes: 23 },

  // 最后90个字符 (711-800) - 完成800字目标
  { id: 711, character: '关系', pinyin: 'guānxì', meaning: 'relationship', components: ['关', '系'], story: 'Close tie = relationship', level: 5, strokes: 14 },
  { id: 712, character: '关心', pinyin: 'guānxīn', meaning: 'care', components: ['关', '心'], story: 'Close heart = care', level: 5, strokes: 12 },
  { id: 713, character: '关于', pinyin: 'guānyú', meaning: 'about', components: ['关', '于'], story: 'Close at = about', level: 5, strokes: 11 },
  { id: 714, character: '观察', pinyin: 'guānchá', meaning: 'observe', components: ['观', '察'], story: 'Look examine = observe', level: 5, strokes: 23 },
  { id: 715, character: '观点', pinyin: 'guāndiǎn', meaning: 'viewpoint', components: ['观', '点'], story: 'Look point = viewpoint', level: 5, strokes: 21 },
  { id: 716, character: '观念', pinyin: 'guānniàn', meaning: 'concept', components: ['观', '念'], story: 'Look think = concept', level: 5, strokes: 26 },
  { id: 717, character: '管理', pinyin: 'guǎnlǐ', meaning: 'manage', components: ['管', '理'], story: 'Tube reason = manage', level: 5, strokes: 19 },
  { id: 718, character: '光明', pinyin: 'guāngmíng', meaning: 'bright', components: ['光', '明'], story: 'Light bright = bright', level: 5, strokes: 14 },
  { id: 719, character: '广告', pinyin: 'guǎnggào', meaning: 'advertisement', components: ['广', '告'], story: 'Wide tell = advertisement', level: 5, strokes: 12 },
  { id: 720, character: '广泛', pinyin: 'guǎngfàn', meaning: 'widespread', components: ['广', '泛'], story: 'Wide float = widespread', level: 5, strokes: 11 },
  { id: 721, character: '规定', pinyin: 'guīdìng', meaning: 'regulation', components: ['规', '定'], story: 'Rule decide = regulation', level: 5, strokes: 16 },
  { id: 722, character: '规律', pinyin: 'guīlǜ', meaning: 'law', components: ['规', '律'], story: 'Rule rhythm = law', level: 5, strokes: 20 },
  { id: 723, character: '规模', pinyin: 'guīmó', meaning: 'scale', components: ['规', '模'], story: 'Rule model = scale', level: 5, strokes: 25 },
  { id: 724, character: '规则', pinyin: 'guīzé', meaning: 'rule', components: ['规', '则'], story: 'Rule then = rule', level: 5, strokes: 20 },
  { id: 725, character: '国际', pinyin: 'guójì', meaning: 'international', components: ['国', '际'], story: 'Country border = international', level: 5, strokes: 19 },
  { id: 726, character: '国内', pinyin: 'guónèi', meaning: 'domestic', components: ['国', '内'], story: 'Country inside = domestic', level: 5, strokes: 12 },
  { id: 727, character: '国外', pinyin: 'guówài', meaning: 'foreign', components: ['国', '外'], story: 'Country outside = foreign', level: 5, strokes: 13 },
  { id: 728, character: '果然', pinyin: 'guǒrán', meaning: 'as expected', components: ['果', '然'], story: 'Fruit so = as expected', level: 5, strokes: 20 },
  { id: 729, character: '过程', pinyin: 'guòchéng', meaning: 'process', components: ['过', '程'], story: 'Pass journey = process', level: 5, strokes: 18 },
  { id: 730, character: '过去', pinyin: 'guòqù', meaning: 'past', components: ['过', '去'], story: 'Pass go = past', level: 5, strokes: 10 },
  { id: 731, character: '还是', pinyin: 'háishì', meaning: 'still', components: ['还', '是'], story: 'Return is = still', level: 5, strokes: 16 },
  { id: 732, character: '海洋', pinyin: 'hǎiyáng', meaning: 'ocean', components: ['海', '洋'], story: 'Sea foreign = ocean', level: 5, strokes: 19 },
  { id: 733, character: '害怕', pinyin: 'hàipà', meaning: 'afraid', components: ['害', '怕'], story: 'Harm fear = afraid', level: 5, strokes: 18 },
  { id: 734, character: '含义', pinyin: 'hányì', meaning: 'meaning', components: ['含', '义'], story: 'Contain meaning = meaning', level: 5, strokes: 16 },
  { id: 735, character: '行为', pinyin: 'xíngwéi', meaning: 'behavior', components: ['行', '为'], story: 'Walk act = behavior', level: 5, strokes: 10 },
  { id: 736, character: '好处', pinyin: 'hǎochù', meaning: 'benefit', components: ['好', '处'], story: 'Good place = benefit', level: 5, strokes: 12 },
  { id: 737, character: '合作', pinyin: 'hézuò', meaning: 'cooperate', components: ['合', '作'], story: 'Join make = cooperate', level: 5, strokes: 12 },
  { id: 738, character: '合适', pinyin: 'héshì', meaning: 'suitable', components: ['合', '适'], story: 'Join fit = suitable', level: 5, strokes: 15 },
  { id: 739, character: '和谐', pinyin: 'héxié', meaning: 'harmonious', components: ['和', '谐'], story: 'Peace harmony = harmonious', level: 5, strokes: 22 },
  { id: 740, character: '黑暗', pinyin: 'hēi\'àn', meaning: 'dark', components: ['黑', '暗'], story: 'Black dark = dark', level: 5, strokes: 25 },
  { id: 741, character: '后果', pinyin: 'hòuguǒ', meaning: 'consequence', components: ['后', '果'], story: 'After fruit = consequence', level: 5, strokes: 15 },
  { id: 742, character: '后来', pinyin: 'hòulái', meaning: 'later', components: ['后', '来'], story: 'After come = later', level: 5, strokes: 15 },
  { id: 743, character: '忽然', pinyin: 'hūrán', meaning: 'suddenly', components: ['忽', '然'], story: 'Neglect so = suddenly', level: 5, strokes: 20 },
  { id: 744, character: '忽视', pinyin: 'hūshì', meaning: 'ignore', components: ['忽', '视'], story: 'Neglect see = ignore', level: 5, strokes: 20 },
  { id: 745, character: '互相', pinyin: 'hùxiāng', meaning: 'mutual', components: ['互', '相'], story: 'Mutual look = mutual', level: 5, strokes: 13 },
  { id: 746, character: '花费', pinyin: 'huāfèi', meaning: 'spend', components: ['花', '费'], story: 'Flower cost = spend', level: 5, strokes: 20 },
  { id: 747, character: '划分', pinyin: 'huàfēn', meaning: 'divide', components: ['划', '分'], story: 'Draw divide = divide', level: 5, strokes: 10 },
  { id: 748, character: '化学', pinyin: 'huàxué', meaning: 'chemistry', components: ['化', '学'], story: 'Change study = chemistry', level: 5, strokes: 12 },
  { id: 749, character: '话题', pinyin: 'huàtí', meaning: 'topic', components: ['话', '题'], story: 'Speech question = topic', level: 5, strokes: 26 },
  { id: 750, character: '怀疑', pinyin: 'huáiyí', meaning: 'doubt', components: ['怀', '疑'], story: 'Embrace doubt = doubt', level: 5, strokes: 21 },
  { id: 751, character: '环节', pinyin: 'huánjié', meaning: 'link', components: ['环', '节'], story: 'Ring section = link', level: 5, strokes: 22 },
  { id: 752, character: '环境', pinyin: 'huánjìng', meaning: 'environment', components: ['环', '境'], story: 'Ring boundary = environment', level: 5, strokes: 22 },
  { id: 753, character: '缓慢', pinyin: 'huǎnmàn', meaning: 'slow', components: ['缓', '慢'], story: 'Slow slow = slow', level: 5, strokes: 29 },
  { id: 754, character: '幻想', pinyin: 'huànxiǎng', meaning: 'fantasy', components: ['幻', '想'], story: 'Illusion think = fantasy', level: 5, strokes: 17 },
  { id: 755, character: '黄金', pinyin: 'huángjīn', meaning: 'gold', components: ['黄', '金'], story: 'Yellow metal = gold', level: 5, strokes: 19 },
  { id: 756, character: '恢复', pinyin: 'huīfù', meaning: 'recover', components: ['恢', '复'], story: 'Restore return = recover', level: 5, strokes: 19 },
  { id: 757, character: '回答', pinyin: 'huídá', meaning: 'answer', components: ['回', '答'], story: 'Return answer = answer', level: 5, strokes: 18 },
  { id: 758, character: '回忆', pinyin: 'huíyì', meaning: 'recall', components: ['回', '忆'], story: 'Return remember = recall', level: 5, strokes: 21 },
  { id: 759, character: '活动', pinyin: 'huódòng', meaning: 'activity', components: ['活', '动'], story: 'Live move = activity', level: 5, strokes: 17 },
  { id: 760, character: '活跃', pinyin: 'huóyuè', meaning: 'active', components: ['活', '跃'], story: 'Live jump = active', level: 5, strokes: 30 },
  { id: 761, character: '火车', pinyin: 'huǒchē', meaning: 'train', components: ['火', '车'], story: 'Fire vehicle = train', level: 5, strokes: 8 },
  { id: 762, character: '获得', pinyin: 'huòdé', meaning: 'obtain', components: ['获', '得'], story: 'Capture get = obtain', level: 5, strokes: 22 },
  { id: 763, character: '机会', pinyin: 'jīhuì', meaning: 'opportunity', components: ['机', '会'], story: 'Machine meet = opportunity', level: 5, strokes: 19 },
  { id: 764, character: '机器', pinyin: 'jīqì', meaning: 'machine', components: ['机', '器'], story: 'Machine tool = machine', level: 5, strokes: 22 },
  { id: 765, character: '积极', pinyin: 'jījí', meaning: 'positive', components: ['积', '极'], story: 'Accumulate extreme = positive', level: 5, strokes: 19 },
  { id: 766, character: '基本', pinyin: 'jīběn', meaning: 'basic', components: ['基', '本'], story: 'Foundation root = basic', level: 5, strokes: 16 },
  { id: 767, character: '基础', pinyin: 'jīchǔ', meaning: 'foundation', components: ['基', '础'], story: 'Foundation base = foundation', level: 5, strokes: 23 },
  { id: 768, character: '即使', pinyin: 'jíshǐ', meaning: 'even if', components: ['即', '使'], story: 'Immediate use = even if', level: 5, strokes: 16 },
  { id: 769, character: '极其', pinyin: 'jíqí', meaning: 'extremely', components: ['极', '其'], story: 'Extreme its = extremely', level: 5, strokes: 20 },
  { id: 770, character: '集中', pinyin: 'jízhōng', meaning: 'concentrate', components: ['集', '中'], story: 'Gather center = concentrate', level: 5, strokes: 16 },
  { id: 771, character: '计划', pinyin: 'jìhuà', meaning: 'plan', components: ['计', '划'], story: 'Count draw = plan', level: 5, strokes: 13 },
  { id: 772, character: '记录', pinyin: 'jìlù', meaning: 'record', components: ['记', '录'], story: 'Remember record = record', level: 5, strokes: 15 },
  { id: 773, character: '记忆', pinyin: 'jìyì', meaning: 'memory', components: ['记', '忆'], story: 'Remember think = memory', level: 5, strokes: 21 },
  { id: 774, character: '技术', pinyin: 'jìshù', meaning: 'technology', components: ['技', '术'], story: 'Skill art = technology', level: 5, strokes: 15 },
  { id: 775, character: '继续', pinyin: 'jìxù', meaning: 'continue', components: ['继', '续'], story: 'Inherit continue = continue', level: 5, strokes: 31 },
  { id: 776, character: '加强', pinyin: 'jiāqiáng', meaning: 'strengthen', components: ['加', '强'], story: 'Add strong = strengthen', level: 5, strokes: 17 },
  { id: 777, character: '家庭', pinyin: 'jiātíng', meaning: 'family', components: ['家', '庭'], story: 'Home court = family', level: 5, strokes: 20 },
  { id: 778, character: '价格', pinyin: 'jiàgé', meaning: 'price', components: ['价', '格'], story: 'Value pattern = price', level: 5, strokes: 16 },
  { id: 779, character: '价值', pinyin: 'jiàzhí', meaning: 'value', components: ['价', '值'], story: 'Price worth = value', level: 5, strokes: 14 },
  { id: 780, character: '假如', pinyin: 'jiǎrú', meaning: 'if', components: ['假', '如'], story: 'False like = if', level: 5, strokes: 17 },
  { id: 781, character: '假设', pinyin: 'jiǎshè', meaning: 'suppose', components: ['假', '设'], story: 'False set = suppose', level: 5, strokes: 22 },
  { id: 782, character: '坚持', pinyin: 'jiānchí', meaning: 'persist', components: ['坚', '持'], story: 'Firm hold = persist', level: 5, strokes: 19 },
  { id: 783, character: '简单', pinyin: 'jiǎndān', meaning: 'simple', components: ['简', '单'], story: 'Simple single = simple', level: 5, strokes: 21 },
  { id: 784, character: '建立', pinyin: 'jiànlì', meaning: 'establish', components: ['建', '立'], story: 'Build stand = establish', level: 5, strokes: 14 },
  { id: 785, character: '建设', pinyin: 'jiànshè', meaning: 'construct', components: ['建', '设'], story: 'Build set = construct', level: 5, strokes: 20 },
  { id: 786, character: '建议', pinyin: 'jiànyì', meaning: 'suggest', components: ['建', '议'], story: 'Build discuss = suggest', level: 5, strokes: 17 },
  { id: 787, character: '健康', pinyin: 'jiànkāng', meaning: 'healthy', components: ['健', '康'], story: 'Strong healthy = healthy', level: 5, strokes: 21 },
  { id: 788, character: '交流', pinyin: 'jiāoliú', meaning: 'exchange', components: ['交', '流'], story: 'Cross flow = exchange', level: 5, strokes: 16 },
  { id: 789, character: '交通', pinyin: 'jiāotōng', meaning: 'traffic', components: ['交', '通'], story: 'Cross through = traffic', level: 5, strokes: 16 },
  { id: 790, character: '教育', pinyin: 'jiàoyù', meaning: 'education', components: ['教', '育'], story: 'Teach nurture = education', level: 5, strokes: 22 },
  { id: 791, character: '接受', pinyin: 'jiēshòu', meaning: 'accept', components: ['接', '受'], story: 'Connect receive = accept', level: 5, strokes: 19 },
  { id: 792, character: '结果', pinyin: 'jiéguǒ', meaning: 'result', components: ['结', '果'], story: 'Tie fruit = result', level: 5, strokes: 17 },
  { id: 793, character: '结合', pinyin: 'jiéhé', meaning: 'combine', components: ['结', '合'], story: 'Tie join = combine', level: 5, strokes: 18 },
  { id: 794, character: '结束', pinyin: 'jiéshù', meaning: 'end', components: ['结', '束'], story: 'Tie bundle = end', level: 5, strokes: 18 },
  { id: 795, character: '解决', pinyin: 'jiějué', meaning: 'solve', components: ['解', '决'], story: 'Untie decide = solve', level: 5, strokes: 20 },
  { id: 796, character: '解释', pinyin: 'jiěshì', meaning: 'explain', components: ['解', '释'], story: 'Untie release = explain', level: 5, strokes: 26 },
  { id: 797, character: '介绍', pinyin: 'jièshào', meaning: 'introduce', components: ['介', '绍'], story: 'Between continue = introduce', level: 5, strokes: 13 },
  { id: 798, character: '经验', pinyin: 'jīngyàn', meaning: 'experience', components: ['经', '验'], story: 'Pass test = experience', level: 5, strokes: 26 },
  { id: 799, character: '精神', pinyin: 'jīngshén', meaning: 'spirit', components: ['精', '神'], story: 'Essence god = spirit', level: 5, strokes: 24 },
  { id: 800, character: '究竟', pinyin: 'jiūjìng', meaning: 'after all', components: ['究', '竟'], story: 'Research complete = after all', level: 5, strokes: 14 }
];

const HanziFlashcardApp = () => {
  // 应用状态管理
  const [currentScreen, setCurrentScreen] = useState<ScreenType>('home');
  const [currentCard, setCurrentCard] = useState(0);
  const [showAnswer, setShowAnswer] = useState(false);
  const [userProgress, setUserProgress] = useState<Record<number, any>>({});
  const [studySession, setStudySession] = useState<HanziCard[]>([]);
  const [sessionStats, setSessionStats] = useState({ correct: 0, total: 0 });
  const [studyMode, setStudyMode] = useState<'recognition' | 'writing' | 'listening'>('recognition');
  const [selectedLevel, setSelectedLevel] = useState(1);
  const [streakCount, setStreakCount] = useState(0);
  const [traceMode, setTraceMode] = useState(false);
  const [favorites, setFavorites] = useState<number[]>([]);
  const [dailyGoal, setDailyGoal] = useState(20);
  const [studyStreak, setStudyStreak] = useState(0);
  const [totalStudyTime, setTotalStudyTime] = useState(0);
  const [sessionStartTime, setSessionStartTime] = useState<number | null>(null);
  const [showStrokeOrder, setShowStrokeOrder] = useState(false);
  const [audioEnabled, setAudioEnabled] = useState(true);
  const [darkMode, setDarkMode] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterLevel, setFilterLevel] = useState<number | null>(null);
  const [showHints, setShowHints] = useState(true);
  const [autoPlay, setAutoPlay] = useState(false);

  // Canvas functionality would be implemented with react-native-canvas or similar

  // 本地存储功能
  useEffect(() => {
    // 加载用户数据
    const savedProgress = localStorage.getItem('hanzi-progress');
    const savedFavorites = localStorage.getItem('hanzi-favorites');
    const savedSettings = localStorage.getItem('hanzi-settings');
    const savedStats = localStorage.getItem('hanzi-stats');

    if (savedProgress) {
      setUserProgress(JSON.parse(savedProgress));
    }
    if (savedFavorites) {
      setFavorites(JSON.parse(savedFavorites));
    }
    if (savedSettings) {
      const settings = JSON.parse(savedSettings);
      setDailyGoal(settings.dailyGoal || 20);
      setShowStrokeOrder(settings.showStrokeOrder || false);
      setAudioEnabled(settings.audioEnabled !== false);
      setDarkMode(settings.darkMode || false);
    }
    if (savedStats) {
      const stats = JSON.parse(savedStats);
      setStudyStreak(stats.studyStreak || 0);
      setTotalStudyTime(stats.totalStudyTime || 0);
    }
  }, []);

  // 保存用户数据
  useEffect(() => {
    localStorage.setItem('hanzi-progress', JSON.stringify(userProgress));
  }, [userProgress]);

  useEffect(() => {
    localStorage.setItem('hanzi-favorites', JSON.stringify(favorites));
  }, [favorites]);

  useEffect(() => {
    const settings = {
      dailyGoal,
      showStrokeOrder,
      audioEnabled,
      darkMode
    };
    localStorage.setItem('hanzi-settings', JSON.stringify(settings));
  }, [dailyGoal, showStrokeOrder, audioEnabled, darkMode]);

  useEffect(() => {
    const stats = {
      studyStreak,
      totalStudyTime
    };
    localStorage.setItem('hanzi-stats', JSON.stringify(stats));
  }, [studyStreak, totalStudyTime]);

  // 计算学习统计
  const getStudyStats = () => {
    const totalCards = Object.keys(userProgress).length;
    const masteredCards = Object.values(userProgress).filter(
      (progress: any) => progress.correct >= 3 && progress.correct / Math.max(1, progress.attempts) >= 0.8
    ).length;
    const todayStudied = Object.values(userProgress).filter(
      (progress: any) => {
        const lastSeen = new Date(progress.lastSeen);
        const today = new Date();
        return lastSeen.toDateString() === today.toDateString();
      }
    ).length;

    return { totalCards, masteredCards, todayStudied };
  };

  // 改进的间隔重复算法 (SRS)
  const calculateNextReview = (attempts: number, correct: number, lastInterval: number = 1) => {
    const accuracy = correct / Math.max(1, attempts);
    let interval = lastInterval;

    if (accuracy >= 0.9) {
      interval = Math.min(interval * 2.5, 30); // 最多30天
    } else if (accuracy >= 0.7) {
      interval = Math.min(interval * 1.5, 14); // 最多14天
    } else if (accuracy >= 0.5) {
      interval = Math.max(interval * 0.8, 1); // 最少1天
    } else {
      interval = 1; // 重新开始
    }

    return Math.round(interval);
  };

  // 初始化学习会话 - 基于改进的间隔重复算法
  const initializeSession = (level: number) => {
    setSessionStartTime(Date.now());
    const levelCards = hanziDatabase.filter(card => card.level <= level);

    // 计算每张卡片的优先级
    const prioritizedCards = levelCards.map(card => {
      const progress = userProgress[card.id] || { attempts: 0, correct: 0, lastSeen: 0, interval: 1 };
      const daysSinceLastSeen = (Date.now() - progress.lastSeen) / (24 * 60 * 60 * 1000);
      const accuracy = progress.correct / Math.max(1, progress.attempts);

      // 优先级计算：新卡片、需要复习的卡片、困难卡片
      let priority = 0;

      if (progress.attempts === 0) {
        priority = 100; // 新卡片最高优先级
      } else if (daysSinceLastSeen >= progress.interval) {
        priority = 80 + (daysSinceLastSeen - progress.interval) * 5; // 需要复习
      } else if (accuracy < 0.6) {
        priority = 60 + (0.6 - accuracy) * 50; // 困难卡片
      } else {
        priority = Math.max(0, 30 - daysSinceLastSeen * 2); // 其他卡片
      }

      return { ...card, priority, progress };
    });

    // 按优先级排序并选择前10张
    const selectedCards = prioritizedCards
      .sort((a, b) => b.priority - a.priority)
      .slice(0, 10)
      .sort(() => Math.random() - 0.5); // 随机打乱顺序

    setStudySession(selectedCards);
    setCurrentCard(0);
    setShowAnswer(false);
    setSessionStats({ correct: 0, total: 0 });
  };

  // 添加到收藏夹 - 使用useCallback优化性能
  const toggleFavorite = useCallback((cardId: number) => {
    setFavorites((prev: number[]) =>
      prev.includes(cardId)
        ? prev.filter((id: number) => id !== cardId)
        : [...prev, cardId]
    );
  }, []);

  // 语音合成 - 使用Expo Speech
  const speakChinese = useCallback(async (text: string) => {
    if (audioEnabled) {
      try {
        await Speech.speak(text, {
          language: 'zh-CN',
          rate: 0.8,
          pitch: 1.0,
        });
      } catch (error) {
        console.log('Speech error:', error);
      }
    }
  }, [audioEnabled]);

  // 搜索和过滤功能
  const filteredCards = useMemo(() => {
    let cards = hanziDatabase;

    // 按等级过滤
    if (filterLevel) {
      cards = cards.filter(card => card.level === filterLevel);
    }

    // 按搜索词过滤
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      cards = cards.filter(card =>
        card.character.includes(term) ||
        card.pinyin.toLowerCase().includes(term) ||
        card.meaning.toLowerCase().includes(term)
      );
    }

    return cards;
  }, [filterLevel, searchTerm]);

  // Removed unused getLearningRecommendations function

  // 处理答案反馈 - 改进版本
  const handleAnswer = (isCorrect: boolean) => {
    const cardId = studySession[currentCard]?.id;
    if (cardId) {
      const currentProgress = userProgress[cardId] || {
        attempts: 0,
        correct: 0,
        lastSeen: 0,
        interval: 1,
        difficulty: 1
      };

      const newInterval = calculateNextReview(
        currentProgress.attempts + 1,
        currentProgress.correct + (isCorrect ? 1 : 0),
        currentProgress.interval
      );

      const newProgress = {
        attempts: currentProgress.attempts + 1,
        correct: currentProgress.correct + (isCorrect ? 1 : 0),
        lastSeen: Date.now(),
        interval: newInterval,
        difficulty: isCorrect ? Math.max(1, currentProgress.difficulty - 0.1) : currentProgress.difficulty + 0.2
      };

      setUserProgress({ ...userProgress, [cardId]: newProgress });
    }

    setSessionStats((prev: { correct: number; total: number }) => ({
      correct: prev.correct + (isCorrect ? 1 : 0),
      total: prev.total + 1
    }));

    if (isCorrect) {
      setStreakCount((prev: number) => prev + 1);
    } else {
      setStreakCount(0);
    }

    // 自动前进到下一张卡片
    setTimeout(() => {
      if (currentCard < studySession.length - 1) {
        setCurrentCard(currentCard + 1);
        setShowAnswer(false);
      } else {
        // 计算学习时间
        if (sessionStartTime) {
          const sessionTime = Math.round((Date.now() - sessionStartTime) / 1000 / 60); // 分钟
          setTotalStudyTime((prev: number) => prev + sessionTime);
        }
        setCurrentScreen('results');
      }
    }, 1500);
  };

  // 绘制功能
  // Canvas drawing functionality would be implemented with react-native-canvas or similar
  // For now, we'll use a placeholder

  const clearCanvas = () => {
    // Canvas clearing functionality would be implemented with react-native-canvas
    console.log('Clear canvas functionality would be implemented here');
  };

  // 主页面组件
  // 主页面组件
  const HomePage = () => {
    const stats = getStudyStats();

    return (
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* 头部 */}
        <View style={styles.header}>
          <View style={[styles.logoContainer, darkMode && styles.darkCard]}>
            <IconBrain size={40} color="#6366f1" />
          </View>
          <Text style={[styles.title, darkMode && styles.darkText]}>汉字大师</Text>
          <Text style={[styles.subtitle, darkMode && styles.darkSubtitle]}>Smart Chinese Character Learning</Text>
        </View>

        {/* Main Content Area View - wraps all sections below header */}
        <View style={{paddingHorizontal: 16}}>

          {/* 统计卡片 */}
          <View style={{flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between', marginBottom: 24}}>
            {[
              { title: '今日学习', value: stats.todayStudied, color: '#4F46E5', Icon: IconCalendar },
              { title: '掌握汉字', value: stats.masteredCards, color: '#10B981', Icon: IconTrophy },
              { title: '连续天数', value: studyStreak, color: '#F59E0B', Icon: IconClock },
              { title: '学习时间', value: `${totalStudyTime}m`, color: '#8B5CF6', Icon: IconTarget },
            ].map((item, index) => (
              <View key={index} style={[{ backgroundColor: darkMode ? '#1A202C' : '#FFFFFF', borderRadius: 16, padding: 16, shadowColor: '#000', shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.05, shadowRadius: 2, elevation: 3, width: '48%', marginBottom: 16 }]}>
                <View style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center'}}>
                  <View>
                    <Text style={{fontSize: 12, color: darkMode ? '#A0AEC0' : '#718096', marginBottom: 4}}>{item.title}</Text>
                    <Text style={{fontSize: 22, fontWeight: 'bold', color: item.color}}>{item.value}</Text>
                  </View>
                  <item.Icon size={28} color={item.color} />
                </View>
              </View>
            ))}
          </View>

          {/* 每日目标进度 */}
          <View style={[{ backgroundColor: darkMode ? '#1A202C' : '#FFFFFF', borderRadius: 20, padding: 20, marginBottom: 24, shadowColor: '#000', shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.05, shadowRadius: 2, elevation: 3 }]}>
            <View style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12}}>
              <Text style={{fontSize: 16, fontWeight: 'bold', color: darkMode ? '#E2E8F0' : '#2D3748'}}>今日目标</Text>
              <Text style={{fontSize: 14, color: darkMode ? '#A0AEC0' : '#718096'}}>
                {stats.todayStudied}/{dailyGoal}
              </Text>
            </View>
            <View style={{backgroundColor: darkMode ? '#2D3748' : '#E2E8F0', borderRadius: 8, height: 10, marginBottom: 8}}>
              <View
                style={{backgroundColor: '#6366F1', height: 10, borderRadius: 8, width: `${Math.min(100, (stats.todayStudied / dailyGoal) * 100)}%` }}
              />
            </View>
            <Text style={{fontSize: 13, color: darkMode ? '#A0AEC0' : '#718096'}}>
              {stats.todayStudied >= dailyGoal ? '🎉 今日目标已完成！' : `还需学习 ${dailyGoal - stats.todayStudied} 个汉字`}
            </Text>
          </View>

          {/* 学习模式选择 */}
          <View style={[{ backgroundColor: darkMode ? '#1A202C' : '#FFFFFF', borderRadius: 20, padding: 20, marginBottom: 24, shadowColor: '#000', shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.05, shadowRadius: 2, elevation: 3 }]}>
            <Text style={{fontSize: 18, fontWeight: 'bold', color: darkMode ? '#E2E8F0' : '#2D3748', marginBottom: 16}}>选择学习模式</Text>
            <View style={{gap: 12}}>
              {[
                { mode: 'recognition', title: 'Recognition Mode', subtitle: 'Character recognition practice', Icon: IconEye, bgColor: '#63B3ED' },
                { mode: 'writing', title: 'Writing Mode', subtitle: 'Practice writing characters', Icon: IconPen, bgColor: '#68D391' },
                { mode: 'listening', title: 'Listening Mode', subtitle: 'Audio-based learning', Icon: IconVolume, bgColor: '#A3A3A3' },
              ].map((item) => (
                <TouchableOpacity
                  key={item.mode}
                  onPress={() => { setStudyMode(item.mode as 'recognition' | 'writing' | 'listening'); setCurrentScreen('levelSelect'); }}
                  style={{backgroundColor: item.bgColor, borderRadius: 12, paddingVertical: 12, paddingHorizontal: 16, flexDirection: 'row', alignItems: 'center', elevation: 2}}
                >
                  <View style={{marginRight: 12}}>
                    <item.Icon size={22} color="#FFFFFF" />
                  </View>
                  <View>
                    <Text style={{fontSize: 15, fontWeight: 'bold', color: '#FFFFFF'}}>{item.title}</Text>
                    <Text style={{fontSize: 12, color: '#FFFFFF', opacity: 0.9}}>{item.subtitle}</Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* 搜索功能 */}
          <View style={[{ backgroundColor: darkMode ? '#1A202C' : '#FFFFFF', borderRadius: 20, padding: 20, marginBottom: 24, shadowColor: '#000', shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.05, shadowRadius: 2, elevation: 3 }]}>
            <Text style={{fontSize: 18, fontWeight: 'bold', color: darkMode ? '#E2E8F0' : '#2D3748', marginBottom: 16}}>搜索汉字</Text>
            <View style={{position: 'relative', marginBottom: 16}}>
              <View style={{position: 'absolute', left: 12, top: 12, zIndex: 1}}>
                <IconSearch size={18} color={darkMode ? '#A0AEC0' : '#A0AEC0'} />
              </View>
              <TextInput
                placeholder="搜索汉字、拼音或含义..."
                value={searchTerm}
                onChangeText={setSearchTerm}
                placeholderTextColor={darkMode ? '#718096' : '#A0AEC0'}
                style={{backgroundColor: darkMode ? '#2D3748' : '#F7FAFC', color: darkMode ? '#E2E8F0' : '#2D3748', borderRadius: 10, paddingVertical: 10, paddingHorizontal: 16, paddingLeft: 38, fontSize: 14, borderWidth: 1, borderColor: darkMode ? '#4A5568' : '#E2E8F0'}}
              />
            </View>
            <View style={{flexDirection: 'row', gap: 8, marginBottom: (searchTerm || filterLevel) ? 16 : 0}}>
              {[1, 2, 3, 4, 5].map(level => (
                <TouchableOpacity
                  key={level}
                  onPress={() => setFilterLevel(filterLevel === level ? null : level)}
                  style={{paddingHorizontal: 12, paddingVertical: 6, borderRadius: 16, backgroundColor: filterLevel === level ? (darkMode ? '#4F46E5' : '#4F46E5') : (darkMode ? '#2D3748' : '#E2E8F0')}}
                >
                  <Text style={{fontSize: 13, fontWeight: '500', color: filterLevel === level ? '#FFFFFF' : (darkMode ? '#A0AEC0' : '#4A5568')}}>HSK {level}</Text>
                </TouchableOpacity>
              ))}
            </View>
            {(searchTerm || filterLevel) && (
              <View>
                <Text style={{fontSize: 13, color: darkMode ? '#A0AEC0' : '#718096', marginBottom: 8}}>
                  找到 {filteredCards.length} 个汉字
                </Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false} style={{maxHeight: 48 * 2 + 8}}>
                  <View style={{flexDirection: 'row', flexWrap: 'wrap', gap: 8}}>
                    {filteredCards.slice(0, 18).map((card: HanziCard) => (
                      <TouchableOpacity
                        key={card.id}
                        onPress={() => speakChinese(card.character)}
                        style={{width: 48, height: 48, borderRadius: 8, backgroundColor: darkMode ? '#2D3748' : '#F0F4F8', justifyContent: 'center', alignItems: 'center'}}

                      >
                        <Text style={{fontSize: 18, fontWeight: 'bold', color: darkMode ? '#E2E8F0' : '#2D3748'}}>{card.character}</Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </ScrollView>
              </View>
            )}
          </View>

          {/* 快速访问 */}
          <View style={{flexDirection: 'row', justifyContent: 'space-around', marginBottom: 24, gap: 12}}>
            {[
              { label: 'Progress', Icon: IconChart, screen: 'progress', badge: null },
              { label: 'Favorites', Icon: IconStar, screen: 'favorites', badge: favorites.length > 0 ? favorites.length : null },
              { label: 'Settings', Icon: IconSettings, screen: 'settings', badge: null },
            ].map((item) => (
              <TouchableOpacity
                key={item.label}
                onPress={() => setCurrentScreen(item.screen as ScreenType)}
                style={{flex: 1, backgroundColor: darkMode ? '#1A202C' : '#FFFFFF', borderRadius: 16, paddingVertical: 16, paddingHorizontal: 8, alignItems: 'center', shadowColor: '#000', shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.05, shadowRadius: 2, elevation: 3}}
              >
                <View style={{marginBottom: 6}}>
                  <item.Icon size={22} color={darkMode ? '#A0AEC0' : '#4A5568'} />
                </View>
                <Text style={{fontSize: 11, fontWeight: '500', color: darkMode ? '#CBD5E0' : '#4A5568', textAlign: 'center'}}>{item.label}</Text>
                {item.badge !== null && (
                  <View style={{position: 'absolute', top: 8, right: 8, backgroundColor: '#F59E0B', borderRadius: 10, minWidth: 18, height: 18, justifyContent: 'center', alignItems: 'center', paddingHorizontal: 5}}>
                    <Text style={{color: 'white', fontSize: 10, fontWeight: 'bold'}}>{item.badge}</Text>
                  </View>
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
    );
  };

  // 等级选择页面
  const LevelSelectPage = () => (
    <ScrollView style={[styles.container, darkMode && styles.darkContainer]}>
      <View style={styles.content}>
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => setCurrentScreen('home')}
            style={[styles.backButton, darkMode && styles.darkCard]}
          >
            <IconRotate size={24} color={darkMode ? '#E2E8F0' : '#4A5568'} />
          </TouchableOpacity>
          <Text style={[styles.title, darkMode && styles.darkText]}>选择难度等级</Text>
        </View>

        <View style={styles.levelContainer}>
          {[1, 2, 3].map(level => (
            <TouchableOpacity
              key={level}
              onPress={() => {
                setSelectedLevel(level);
                initializeSession(level);
                setCurrentScreen('study');
              }}
              style={[styles.levelCard, darkMode && styles.darkCard]}
            >
              <View style={styles.levelContent}>
                <View style={styles.levelInfo}>
                  <Text style={[styles.levelTitle, darkMode && styles.darkText]}>HSK Level {level}</Text>
                  <Text style={[styles.levelDescription, darkMode && styles.darkSubtitle]}>
                    {level === 1 && "Basic characters (150 characters)"}
                    {level === 2 && "Elementary level (300 characters)"}
                    {level === 3 && "Intermediate level (600 characters)"}
                  </Text>
                  <View style={styles.masteredInfo}>
                    <IconStar size={16} color="#10B981" filled />
                    <Text style={styles.masteredText}>
                      Mastered: {hanziDatabase.filter(card =>
                        card.level <= level &&
                        userProgress[card.id]?.correct > 2
                      ).length}
                    </Text>
                  </View>
                </View>
                <IconTarget size={32} color="#6366f1" />
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </ScrollView>
  );

  // 学习页面
  const StudyPage = () => {
    if (studySession.length === 0) return null;

    const card = studySession[currentCard];
    if (!card) return null;

    // 自动播放音频效果
    useEffect(() => {
      if (autoPlay && card && !showAnswer) {
        const timer = setTimeout(() => {
          speakChinese(card.character);
        }, 500);
        return () => clearTimeout(timer);
      }
    }, [currentCard, autoPlay, card, showAnswer, speakChinese]);

    return (
      <ScrollView style={[styles.container, darkMode && styles.darkContainer]}>
        <View style={styles.content}>
          {/* 头部进度 */}
          <View style={styles.studyHeader}>
            <TouchableOpacity
              onPress={() => setCurrentScreen('home')}
              style={[styles.backButton, darkMode && styles.darkCard]}
            >
              <IconRotate size={24} color={darkMode ? '#E2E8F0' : '#4A5568'} />
            </TouchableOpacity>
            <View style={styles.progressContainer}>
              <View style={[styles.progressBar, darkMode && { backgroundColor: '#374151' }]}>
                <View
                  style={[
                    styles.progressFill,
                    { width: `${((currentCard + 1) / studySession.length) * 100}%` }
                  ]}
                />
              </View>
              <Text style={[styles.progressText, darkMode && styles.darkSubtitle]}>
                {currentCard + 1} / {studySession.length}
              </Text>
            </View>
            <View style={styles.streakContainer}>
              <Text style={[styles.streakLabel, darkMode && styles.darkSubtitle]}>Streak</Text>
              <Text style={styles.streakValue}>{streakCount}</Text>
            </View>
          </View>

          {/* 汉字卡片 */}
          <View style={[styles.hanziCard, darkMode && styles.darkCard]}>
            {studyMode === 'recognition' && (
              <>
                <Text style={[styles.hanziCharacter, darkMode && styles.darkText]}>{card.character}</Text>
                <View style={styles.cardControls}>
                  <TouchableOpacity
                    onPress={() => speakChinese(card.character)}
                    style={styles.audioButton}
                  >
                    <IconVolume size={24} color="#6366f1" />
                  </TouchableOpacity>
                  {showHints && (
                    <View style={styles.hintsContainer}>
                      <IconLightbulb size={16} color="#6b7280" />
                      <Text style={[styles.hintsText, darkMode && styles.darkSubtitle]}>
                        Strokes: {card.strokes}
                      </Text>
                    </View>
                  )}
                </View>

                {/* 自动播放音频 */}
                {autoPlay && !showAnswer && (
                  <Text style={[styles.autoPlayText, darkMode && styles.darkSubtitle]}>
                    🔊 Auto-playing pronunciation...
                  </Text>
                )}

                {showAnswer && (
                  <View style={styles.answerContainer}>
                    <View style={styles.pinyinContainer}>
                      <Text style={styles.hanziPinyin}>{card.pinyin}</Text>
                      <TouchableOpacity
                        onPress={() => toggleFavorite(card.id)}
                        style={[
                          styles.favoriteButton,
                          favorites.includes(card.id) && styles.favoriteButtonActive
                        ]}
                      >
                        <IconStar
                          size={20}
                          color={favorites.includes(card.id) ? '#f59e0b' : '#9ca3af'}
                          filled={favorites.includes(card.id)}
                        />
                      </TouchableOpacity>
                    </View>
                    <Text style={[styles.hanziMeaning, darkMode && styles.darkText]}>{card.meaning}</Text>
                    <View style={[styles.storyContainer, darkMode && { backgroundColor: '#374151' }]}>
                      <Text style={[styles.storyLabel, darkMode && styles.darkSubtitle]}>Memory Story:</Text>
                      <Text style={[styles.storyText, darkMode && styles.darkText]}>{card.story}</Text>
                    </View>
                    <Text style={[styles.detailsText, darkMode && styles.darkSubtitle]}>
                      Strokes: {card.strokes} | Components: {card.components.join(', ')}
                    </Text>
                  </View>
                )}
              </>
            )}

            {studyMode === 'writing' && (
              <>
                <Text style={[styles.hanziMeaning, darkMode && styles.darkText]}>{card.meaning}</Text>
                <Text style={styles.hanziPinyin}>{card.pinyin}</Text>

                <View style={styles.writingContainer}>
                  <View style={styles.canvasPlaceholder}>
                    <Text style={styles.canvasText}>Writing Practice Area</Text>
                    {traceMode && (
                      <Text style={styles.traceCharacter}>{card.character}</Text>
                    )}
                  </View>
                </View>

                <View style={styles.writingControls}>
                  <TouchableOpacity
                    onPress={() => setTraceMode(!traceMode)}
                    style={[
                      styles.controlButton,
                      traceMode ? styles.controlButtonActive : styles.controlButtonInactive
                    ]}
                  >
                    <Text style={[
                      styles.controlButtonText,
                      traceMode ? styles.controlButtonTextActive : styles.controlButtonTextInactive
                    ]}>
                      Trace Mode
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={clearCanvas}
                    style={[styles.controlButton, styles.controlButtonInactive]}
                  >
                    <Text style={styles.controlButtonTextInactive}>Clear</Text>
                  </TouchableOpacity>
                </View>

                {showAnswer && (
                  <View style={styles.answerContainer}>
                    <Text style={[styles.hanziCharacter, { fontSize: 80 }, darkMode && styles.darkText]}>
                      {card.character}
                    </Text>
                    <View style={[styles.storyContainer, darkMode && { backgroundColor: '#374151' }]}>
                      <Text style={[styles.storyLabel, darkMode && styles.darkSubtitle]}>
                        Stroke Order & Story:
                      </Text>
                      <Text style={[styles.storyText, darkMode && styles.darkText]}>{card.story}</Text>
                    </View>
                  </View>
                )}
              </>
            )}

            {studyMode === 'listening' && (
              <>
                <View style={styles.listeningContainer}>
                  <TouchableOpacity
                    onPress={() => speakChinese(card.character)}
                    style={styles.listeningButton}
                  >
                    <IconVolume size={48} color="#ffffff" />
                  </TouchableOpacity>
                  <Text style={[styles.listeningText, darkMode && styles.darkSubtitle]}>
                    Listen and choose the correct character
                  </Text>
                </View>

                {showAnswer && (
                  <View style={styles.answerContainer}>
                    <Text style={[styles.hanziCharacter, darkMode && styles.darkText]}>{card.character}</Text>
                    <Text style={styles.hanziPinyin}>{card.pinyin}</Text>
                    <Text style={[styles.hanziMeaning, darkMode && styles.darkText]}>{card.meaning}</Text>
                  </View>
                )}
              </>
            )}
          </View>

          {/* 控制按钮 */}
          {!showAnswer ? (
            <TouchableOpacity
              onPress={() => setShowAnswer(true)}
              style={styles.buttonPrimary}
            >
              <Text style={styles.buttonPrimaryText}>Show Answer</Text>
            </TouchableOpacity>
          ) : (
            <View style={styles.buttonRow}>
              <TouchableOpacity
                onPress={() => handleAnswer(false)}
                style={[styles.buttonHalf, styles.buttonDanger]}
              >
                <IconX size={24} color="#ffffff" />
                <Text style={[styles.buttonPrimaryText, { marginLeft: 8 }]}>Hard</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => handleAnswer(true)}
                style={[styles.buttonHalf, styles.buttonSuccess]}
              >
                <IconCheck size={24} color="#ffffff" />
                <Text style={[styles.buttonPrimaryText, { marginLeft: 8 }]}>Easy</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </ScrollView>
    );
  };

  // 结果页面
  const ResultsPage = () => (
    <ScrollView style={[styles.container, darkMode && styles.darkContainer]}>
      <View style={styles.content}>
        <View style={[styles.card, darkMode && styles.darkCard, { alignItems: 'center' }]}>
          <Text style={styles.celebrationEmoji}>🎉</Text>
          <Text style={[styles.resultsTitle, darkMode && styles.darkText]}>Session Complete!</Text>

          <View style={styles.statsGrid}>
            <View style={[styles.statCard, styles.correctStatCard]}>
              <Text style={styles.statNumber}>{sessionStats.correct}</Text>
              <Text style={styles.correctStatLabel}>Correct</Text>
            </View>
            <View style={[styles.statCard, styles.totalStatCard]}>
              <Text style={styles.statNumberBlue}>{sessionStats.total}</Text>
              <Text style={styles.totalStatLabel}>Total</Text>
            </View>
          </View>

          <Text style={[styles.accuracyText, darkMode && styles.darkText]}>
            Accuracy: {Math.round((sessionStats.correct / sessionStats.total) * 100)}%
          </Text>

          <View style={styles.resultsButtons}>
            <TouchableOpacity
              onPress={() => {
                initializeSession(selectedLevel);
                setCurrentScreen('study');
              }}
              style={styles.buttonPrimary}
            >
              <Text style={styles.buttonPrimaryText}>Study Again</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => setCurrentScreen('home')}
              style={[styles.buttonSecondary, darkMode && styles.buttonSecondaryDark]}
            >
              <Text style={[styles.buttonSecondaryText, darkMode && styles.buttonSecondaryTextDark]}>
                Back to Home
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </ScrollView>
  );

  // 进度页面
  const ProgressPage = () => {
    return (
      <ScrollView style={[styles.container, darkMode && styles.darkContainer]}>
        <View style={styles.content}>
          <View style={styles.header}>
            <TouchableOpacity
              onPress={() => setCurrentScreen('home')}
              style={[styles.backButton, darkMode && styles.darkCard]}
            >
              <IconRotate size={24} color={darkMode ? '#E2E8F0' : '#4A5568'} />
            </TouchableOpacity>
            <Text style={[styles.title, darkMode && styles.darkText]}>Learning Progress</Text>
          </View>

          <View style={[styles.card, darkMode && styles.darkCard]}>
            <Text style={[styles.cardTitle, darkMode && styles.darkText]}>Your Statistics</Text>

            {[1, 2, 3].map(level => {
              const levelCards = hanziDatabase.filter(card => card.level === level);
              const masteredCards = levelCards.filter(card =>
                userProgress[card.id]?.correct > 2
              );

              return (
                <View key={level} style={styles.progressLevelContainer}>
                  <View style={styles.progressLevelHeader}>
                    <Text style={[styles.progressLevelTitle, darkMode && styles.darkText]}>
                      HSK Level {level}
                    </Text>
                    <Text style={[styles.progressLevelStats, darkMode && styles.darkSubtitle]}>
                      {masteredCards.length}/{levelCards.length}
                    </Text>
                  </View>
                  <View style={[styles.progressBar, darkMode && { backgroundColor: '#374151' }]}>
                    <View
                      style={[
                        styles.progressFill,
                        {
                          width: `${(masteredCards.length / levelCards.length) * 100}%`,
                          backgroundColor: '#10b981'
                        }
                      ]}
                    />
                  </View>
                </View>
              );
            })}
          </View>
        </View>
      </ScrollView>
    );
  };

  // 收藏夹页面
  const FavoritesPage = () => {
    const favoriteCards = hanziDatabase.filter(card => favorites.includes(card.id));

    return (
      <ScrollView style={[styles.container, darkMode && styles.darkContainer]}>
        <View style={styles.content}>
          <View style={styles.header}>
            <TouchableOpacity
              onPress={() => setCurrentScreen('home')}
              style={[styles.backButton, darkMode && styles.darkCard]}
            >
              <IconRotate size={24} color={darkMode ? '#E2E8F0' : '#4A5568'} />
            </TouchableOpacity>
            <Text style={[styles.title, darkMode && styles.darkText]}>Favorites</Text>
          </View>

          {favoriteCards.length === 0 ? (
            <View style={[styles.card, darkMode && styles.darkCard, { alignItems: 'center' }]}>
              <IconStar size={64} color={darkMode ? '#4b5563' : '#d1d5db'} />
              <Text style={[styles.emptyStateTitle, darkMode && styles.darkText]}>No Favorites Yet</Text>
              <Text style={[styles.emptyStateText, darkMode && styles.darkSubtitle]}>
                Start learning and add characters to your favorites by tapping the star icon!
              </Text>
            </View>
          ) : (
            <View>
              {favoriteCards.map(card => (
                <View key={card.id} style={[styles.favoriteCard, darkMode && styles.darkCard]}>
                  <View style={styles.favoriteCardContent}>
                    <View style={styles.favoriteCardLeft}>
                      <Text style={[styles.favoriteCharacter, darkMode && styles.darkText]}>
                        {card.character}
                      </Text>
                      <View style={styles.favoriteCardInfo}>
                        <Text style={[styles.favoritePinyin, darkMode && styles.darkText]}>
                          {card.pinyin}
                        </Text>
                        <Text style={[styles.favoriteMeaning, darkMode && styles.darkSubtitle]}>
                          {card.meaning}
                        </Text>
                        <Text style={[styles.favoriteDetails, darkMode && styles.darkSubtitle]}>
                          HSK {card.level} • {card.strokes} strokes
                        </Text>
                      </View>
                    </View>
                    <View style={styles.favoriteCardActions}>
                      <TouchableOpacity
                        onPress={() => speakChinese(card.character)}
                        style={[styles.favoriteActionButton, darkMode && styles.favoriteActionButtonDark]}
                      >
                        <IconVolume size={16} color={darkMode ? '#d1d5db' : '#4b5563'} />
                      </TouchableOpacity>
                      <TouchableOpacity
                        onPress={() => toggleFavorite(card.id)}
                        style={styles.favoriteStarButton}
                      >
                        <IconStar size={16} color="#f59e0b" filled />
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              ))}

              {favoriteCards.length > 0 && (
                <TouchableOpacity
                  onPress={() => {
                    // 创建收藏夹学习会话
                    setStudySession(favoriteCards.sort(() => Math.random() - 0.5).slice(0, 10));
                    setCurrentCard(0);
                    setShowAnswer(false);
                    setSessionStats({ correct: 0, total: 0 });
                    setSessionStartTime(Date.now());
                    setCurrentScreen('study');
                  }}
                  style={styles.studyFavoritesButton}
                >
                  <Text style={styles.buttonPrimaryText}>Study Favorites</Text>
                </TouchableOpacity>
              )}
            </View>
          )}
        </View>
      </ScrollView>
    );
  };

  // 设置页面
  const SettingsPage = () => {
    return (
      <ScrollView style={[styles.container, darkMode && styles.darkContainer]}>
        <View style={styles.content}>
          <View style={styles.header}>
            <TouchableOpacity
              onPress={() => setCurrentScreen('home')}
              style={[styles.backButton, darkMode && styles.darkCard]}
            >
              <IconRotate size={24} color={darkMode ? '#E2E8F0' : '#4A5568'} />
            </TouchableOpacity>
            <Text style={[styles.title, darkMode && styles.darkText]}>Settings</Text>
          </View>

          <View style={[styles.card, darkMode && styles.darkCard]}>
            <Text style={[styles.cardTitle, darkMode && styles.darkText]}>Learning Preferences</Text>

            <View style={styles.settingsContainer}>
              <View style={styles.settingRow}>
                <Text style={[styles.settingLabel, darkMode && styles.darkText]}>Audio Pronunciation</Text>
                <TouchableOpacity
                  onPress={() => setAudioEnabled(!audioEnabled)}
                  style={[
                    styles.toggleSwitch,
                    audioEnabled ? styles.toggleSwitchActive : styles.toggleSwitchInactive
                  ]}
                >
                  <View style={[
                    styles.toggleThumb,
                    audioEnabled ? styles.toggleThumbActive : styles.toggleThumbInactive
                  ]} />
                </TouchableOpacity>
              </View>

              <View style={styles.settingRow}>
                <Text style={[styles.settingLabel, darkMode && styles.darkText]}>Show Stroke Order</Text>
                <TouchableOpacity
                  onPress={() => setShowStrokeOrder(!showStrokeOrder)}
                  style={[
                    styles.toggleSwitch,
                    showStrokeOrder ? styles.toggleSwitchActive : styles.toggleSwitchInactive
                  ]}
                >
                  <View style={[
                    styles.toggleThumb,
                    showStrokeOrder ? styles.toggleThumbActive : styles.toggleThumbInactive
                  ]} />
                </TouchableOpacity>
              </View>

              <View style={styles.settingRow}>
                <Text style={[styles.settingLabel, darkMode && styles.darkText]}>Dark Mode</Text>
                <TouchableOpacity
                  onPress={() => setDarkMode(!darkMode)}
                  style={[
                    styles.toggleSwitch,
                    darkMode ? styles.toggleSwitchActive : styles.toggleSwitchInactive
                  ]}
                >
                  <View style={[
                    styles.toggleThumb,
                    darkMode ? styles.toggleThumbActive : styles.toggleThumbInactive
                  ]} />
                </TouchableOpacity>
              </View>

              <View style={styles.settingRow}>
                <Text style={[styles.settingLabel, darkMode && styles.darkText]}>Show Hints</Text>
                <TouchableOpacity
                  onPress={() => setShowHints(!showHints)}
                  style={[
                    styles.toggleSwitch,
                    showHints ? styles.toggleSwitchActive : styles.toggleSwitchInactive
                  ]}
                >
                  <View style={[
                    styles.toggleThumb,
                    showHints ? styles.toggleThumbActive : styles.toggleThumbInactive
                  ]} />
                </TouchableOpacity>
              </View>

              <View style={styles.settingRow}>
                <Text style={[styles.settingLabel, darkMode && styles.darkText]}>Auto Play Audio</Text>
                <TouchableOpacity
                  onPress={() => setAutoPlay(!autoPlay)}
                  style={[
                    styles.toggleSwitch,
                    autoPlay ? styles.toggleSwitchActive : styles.toggleSwitchInactive
                  ]}
                >
                  <View style={[
                    styles.toggleThumb,
                    autoPlay ? styles.toggleThumbActive : styles.toggleThumbInactive
                  ]} />
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {/* 每日目标设置 */}
          <View style={[styles.card, darkMode && styles.darkCard]}>
            <Text style={[styles.cardTitle, darkMode && styles.darkText]}>Daily Goal</Text>
            <View style={styles.dailyGoalContainer}>
              <Text style={[styles.settingLabel, darkMode && styles.darkText]}>Characters per day:</Text>
              <View style={styles.dailyGoalControls}>
                <TouchableOpacity
                  onPress={() => setDailyGoal(Math.max(5, dailyGoal - 5))}
                  style={styles.dailyGoalButton}
                >
                  <Text style={styles.dailyGoalButtonText}>-</Text>
                </TouchableOpacity>
                <Text style={[styles.dailyGoalValue, darkMode && styles.darkText]}>
                  {dailyGoal}
                </Text>
                <TouchableOpacity
                  onPress={() => setDailyGoal(Math.min(100, dailyGoal + 5))}
                  style={styles.dailyGoalButton}
                >
                  <Text style={styles.dailyGoalButtonText}>+</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {/* 数据管理 */}
          <View style={[styles.card, darkMode && styles.darkCard]}>
            <Text style={[styles.cardTitle, darkMode && styles.darkText]}>Data Management</Text>
            <View style={styles.dataManagementContainer}>
              <TouchableOpacity
                onPress={() => {
                  Alert.alert(
                    'Reset Progress',
                    'Are you sure you want to reset all progress? This cannot be undone.',
                    [
                      { text: 'Cancel', style: 'cancel' },
                      {
                        text: 'Reset',
                        style: 'destructive',
                        onPress: () => {
                          setUserProgress({});
                          setFavorites([]);
                          setStudyStreak(0);
                          setTotalStudyTime(0);
                        }
                      }
                    ]
                  );
                }}
                style={styles.resetButton}
              >
                <Text style={styles.buttonPrimaryText}>Reset All Progress</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  Alert.alert('Export Data', 'Data export functionality would be implemented here for React Native.');
                }}
                style={[styles.buttonSecondary, darkMode && styles.buttonSecondaryDark]}
              >
                <Text style={[styles.buttonSecondaryText, darkMode && styles.buttonSecondaryTextDark]}>
                  Export Data
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>
    );
  };

  // 渲染当前屏幕
  const renderCurrentScreen = () => {
    switch (currentScreen) {
      case 'home':
        return <HomePage />;
      case 'levelSelect':
        return <LevelSelectPage />;
      case 'study':
        return <StudyPage />;
      case 'results':
        return <ResultsPage />;
      case 'progress':
        return <ProgressPage />;
      case 'favorites':
        return <FavoritesPage />;
      case 'settings':
        return <SettingsPage />;
      default:
        return <HomePage />;
    }
  };

  return (
    <SafeAreaView style={[styles.container, darkMode && styles.darkContainer]}>
      <StatusBar barStyle={darkMode ? 'light-content' : 'dark-content'} />
      {renderCurrentScreen()}
    </SafeAreaView>
  );
};

// React Native 样式
const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  darkContainer: {
    backgroundColor: '#1f2937',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    alignItems: 'center',
    marginVertical: 30,
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#ffffff',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  darkCard: {
    backgroundColor: '#374151',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  darkText: {
    color: '#ffffff',
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
  },
  darkSubtitle: {
    color: '#d1d5db',
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  statCard: {
    width: (width - 60) / 2 - 8,
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  statCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  statLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 4,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  card: {
    backgroundColor: '#ffffff',
    borderRadius: 24,
    padding: 24,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
  },
  modeButton: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  modeButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modeButtonText: {
    marginLeft: 12,
  },
  modeButtonTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  modeButtonSubtitle: {
    fontSize: 14,
    color: '#ffffff',
    opacity: 0.9,
  },
  searchInput: {
    borderRadius: 16,
    padding: 12,
    paddingLeft: 40,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    backgroundColor: '#f9fafb',
    fontSize: 16,
    marginBottom: 16,
  },
  darkSearchInput: {
    backgroundColor: '#374151',
    borderColor: '#4b5563',
    color: '#ffffff',
  },
  levelButtonsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  levelButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginRight: 8,
    marginBottom: 8,
  },
  levelButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  quickAccessContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickAccessButton: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  quickAccessText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#374151',
    marginTop: 4,
  },
  hanziCard: {
    backgroundColor: '#ffffff',
    borderRadius: 24,
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 400,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  hanziCharacter: {
    fontSize: 120,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 24,
  },
  hanziPinyin: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#6366f1',
    marginBottom: 8,
  },
  hanziMeaning: {
    fontSize: 18,
    color: '#374151',
    marginBottom: 16,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#e5e7eb',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#6366f1',
  },
  buttonPrimary: {
    backgroundColor: '#6366f1',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonPrimaryText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  buttonHalf: {
    flex: 1,
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 4,
  },
  buttonSuccess: {
    backgroundColor: '#10b981',
  },
  buttonDanger: {
    backgroundColor: '#ef4444',
  },
  centerText: {
    textAlign: 'center',
  },
  mb16: {
    marginBottom: 16,
  },
  mb24: {
    marginBottom: 24,
  },
  content: {
    padding: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#ffffff',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  levelContainer: {
    marginTop: 20,
  },
  levelCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  levelContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  levelInfo: {
    flex: 1,
  },
  levelTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  levelDescription: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 8,
  },
  masteredInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  masteredText: {
    fontSize: 12,
    color: '#10b981',
    marginLeft: 4,
  },
  studyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  progressContainer: {
    flex: 1,
    marginHorizontal: 16,
  },
  progressText: {
    textAlign: 'center',
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
  },
  streakContainer: {
    alignItems: 'flex-end',
  },
  streakLabel: {
    fontSize: 12,
    color: '#6b7280',
  },
  streakValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#f59e0b',
  },
  cardControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  audioButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#e0e7ff',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  hintsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  hintsText: {
    fontSize: 14,
    color: '#6b7280',
    marginLeft: 8,
  },
  autoPlayText: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 16,
  },
  answerContainer: {
    marginTop: 16,
  },
  pinyinContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  favoriteButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f3f4f6',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 16,
  },
  favoriteButtonActive: {
    backgroundColor: '#fef3c7',
  },
  storyContainer: {
    backgroundColor: '#eff6ff',
    borderRadius: 16,
    padding: 16,
    marginVertical: 16,
  },
  storyLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 8,
  },
  storyText: {
    fontSize: 14,
    color: '#1f2937',
    lineHeight: 20,
  },
  detailsText: {
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'center',
  },
  writingContainer: {
    marginVertical: 20,
  },
  canvasPlaceholder: {
    width: 200,
    height: 200,
    borderWidth: 2,
    borderColor: '#d1d5db',
    borderStyle: 'dashed',
    borderRadius: 16,
    backgroundColor: '#f9fafb',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    position: 'relative',
  },
  canvasText: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
  },
  traceCharacter: {
    fontSize: 120,
    color: '#e5e7eb',
    position: 'absolute',
    fontWeight: 'bold',
  },
  writingControls: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 20,
    marginBottom: 20,
  },
  controlButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginHorizontal: 8,
  },
  controlButtonActive: {
    backgroundColor: '#6366f1',
  },
  controlButtonInactive: {
    backgroundColor: '#e5e7eb',
  },
  controlButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  controlButtonTextActive: {
    color: '#ffffff',
  },
  controlButtonTextInactive: {
    color: '#374151',
  },
  listeningContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  listeningButton: {
    width: 96,
    height: 96,
    borderRadius: 48,
    backgroundColor: '#8b5cf6',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  listeningText: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
  celebrationEmoji: {
    fontSize: 60,
    textAlign: 'center',
    marginBottom: 16,
  },
  resultsTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1f2937',
    textAlign: 'center',
    marginBottom: 24,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  correctStatCard: {
    backgroundColor: '#dcfce7',
  },
  totalStatCard: {
    backgroundColor: '#dbeafe',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#16a34a',
    textAlign: 'center',
  },
  statNumberBlue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2563eb',
    textAlign: 'center',
  },
  correctStatLabel: {
    fontSize: 12,
    color: '#15803d',
    textAlign: 'center',
  },
  totalStatLabel: {
    fontSize: 12,
    color: '#1d4ed8',
    textAlign: 'center',
  },
  accuracyText: {
    fontSize: 18,
    color: '#374151',
    textAlign: 'center',
    marginBottom: 24,
  },
  resultsButtons: {
    gap: 12,
  },
  buttonSecondary: {
    backgroundColor: '#e5e7eb',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonSecondaryDark: {
    backgroundColor: '#4b5563',
  },
  buttonSecondaryText: {
    color: '#374151',
    fontSize: 16,
    fontWeight: '600',
  },
  buttonSecondaryTextDark: {
    color: '#e5e7eb',
  },
  progressLevelContainer: {
    marginBottom: 24,
  },
  progressLevelHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLevelTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
  },
  progressLevelStats: {
    fontSize: 14,
    color: '#6b7280',
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateText: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 20,
  },
  favoriteCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  favoriteCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  favoriteCardLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  favoriteCharacter: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#1f2937',
    marginRight: 16,
  },
  favoriteCardInfo: {
    flex: 1,
  },
  favoritePinyin: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 2,
  },
  favoriteMeaning: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 2,
  },
  favoriteDetails: {
    fontSize: 12,
    color: '#9ca3af',
  },
  favoriteCardActions: {
    flexDirection: 'row',
    gap: 8,
  },
  favoriteActionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f3f4f6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  favoriteActionButtonDark: {
    backgroundColor: '#374151',
  },
  favoriteStarButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#fef3c7',
    alignItems: 'center',
    justifyContent: 'center',
  },
  studyFavoritesButton: {
    backgroundColor: '#f59e0b',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
  },
  settingsContainer: {
    gap: 16,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  settingLabel: {
    fontSize: 16,
    color: '#374151',
  },
  toggleSwitch: {
    width: 48,
    height: 24,
    borderRadius: 12,
    padding: 2,
    justifyContent: 'center',
  },
  toggleSwitchActive: {
    backgroundColor: '#6366f1',
  },
  toggleSwitchInactive: {
    backgroundColor: '#d1d5db',
  },
  toggleThumb: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#ffffff',
  },
  toggleThumbActive: {
    alignSelf: 'flex-end',
  },
  toggleThumbInactive: {
    alignSelf: 'flex-start',
  },
  dailyGoalContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dailyGoalControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  dailyGoalButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#e5e7eb',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dailyGoalButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4b5563',
  },
  dailyGoalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    minWidth: 48,
    textAlign: 'center',
  },
  dataManagementContainer: {
    gap: 12,
  },
  resetButton: {
    backgroundColor: '#ef4444',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default HanziFlashcardApp;
